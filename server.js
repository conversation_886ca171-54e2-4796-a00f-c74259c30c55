// Node.js 静态文件服务器
// 用于开发环境启动甘特图管理系统

const http = require('http');
const fs = require('fs');
const path = require('path');

const PORT = process.env.PORT || 8080;
const HOST = process.env.HOST || 'localhost';

// MIME类型映射
const mimeTypes = {
    '.html': 'text/html',
    '.js': 'text/javascript',
    '.css': 'text/css',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
    '.ico': 'image/x-icon',
    '.csv': 'text/csv',
    '.txt': 'text/plain'
};

// 获取MIME类型
function getMimeType(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    return mimeTypes[ext] || 'application/octet-stream';
}

// 创建HTTP服务器
const server = http.createServer((req, res) => {
    // 解析请求URL
    let filePath = req.url === '/' ? '/index.html' : req.url;

    // 移除查询参数
    filePath = filePath.split('?')[0];

    // 构建完整文件路径
    const fullPath = path.join(__dirname, filePath);

    // 检查文件是否存在
    fs.readFile(fullPath, (err, content) => {
        if (err) {
            // 文件不存在，返回404
            res.writeHead(404, { 'Content-Type': 'text/html' });
            res.end(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>404 - 文件未找到</title>
                    <meta charset="UTF-8">
                </head>
                <body>
                    <h1>404 - 文件未找到</h1>
                    <p>请求的文件 ${filePath} 不存在</p>
                    <p><a href="/">返回首页</a></p>
                </body>
                </html>
            `);
            console.log(`❌ ${new Date().toLocaleTimeString()} - ${req.method} ${req.url} - 404`);
        } else {
            // 文件存在，返回内容
            const mimeType = getMimeType(fullPath);

            res.writeHead(200, {
                'Content-Type': mimeType,
                'Content-Length': content.length,
                'Cache-Control': 'no-cache',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            });

            res.end(content);
            console.log(`✅ ${new Date().toLocaleTimeString()} - ${req.method} ${req.url} - 200`);
        }
    });
});

// 启动服务器
server.listen(PORT, HOST, () => {
    console.log('🚀 甘特图管理系统服务器启动成功!');
    console.log(`📍 服务器地址: http://${HOST}:${PORT}`);
    console.log(`📁 服务目录: ${__dirname}`);
    console.log(`⏰ 启动时间: ${new Date().toLocaleString()}`);
    console.log('');
    console.log('📋 可用的URL:');
    console.log(`   主页: http://${HOST}:${PORT}/`);
    console.log(`   甘特图: http://${HOST}:${PORT}/index.html`);
    console.log('');
    console.log('💡 提示: 按 Ctrl+C 停止服务器');
    console.log('='.repeat(50));
});

// 优雅关闭
process.on('SIGINT', () => {
    console.log('\n🛑 正在关闭服务器...');
    server.close(() => {
        console.log('✅ 服务器已关闭');
        process.exit(0);
    });
});

// 错误处理
server.on('error', (error) => {
    if (error.code === 'EADDRINUSE') {
        console.error(`❌ 端口 ${PORT} 已被占用，请尝试其他端口`);
        console.error(`💡 您可以使用: PORT=3001 npm start`);
    } else {
        console.error('❌ 服务器启动失败:', error.message);
    }
    process.exit(1);
});