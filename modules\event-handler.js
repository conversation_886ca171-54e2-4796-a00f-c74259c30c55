// 事件处理模块
// 负责用户交互事件的处理，包括按钮点击、表单提交等

import {
    tasks,
    groups,
    addTask,
    updateTask,
    deleteTask,
    deleteTasks,
    copyTasks,
    moveTasks,
    updateTasksGroup,
    updateTasksColor,
    moveTasksDates,
    addGroup,
    deleteGroup,
    setSelectedTaskId,
    getSelectedTaskId,
    saveTasks,
    saveGroups
} from './data-manager.js';
import { renderTasks, renderGanttChart, updateSelectAllCheckbox } from './ui-renderer.js';
import { addDays, formatDateString } from './utils.js';

// DOM 元素引用
let taskModal, groupModal, columnModal, borderModal, modalTitle, taskForm, taskId, taskGroup, taskNumber, taskName, taskDuration, taskStart, taskEnd, taskColor;
let groupName, groupList, addGroupSubmitBtn;
let addTaskBtn, editTaskBtn, deleteTaskBtn, copyTaskBtn, moveUpBtn, moveDownBtn, moveLeftBtn, moveRightBtn;
let batchGroupBtn, batchColorBtn, groupBtn, columnBtn, borderBtn, exportBtn, importBtn;

// 初始化DOM元素引用
export function initDOMElements() {
    // 模态框元素
    taskModal = document.getElementById('taskModal');
    groupModal = document.getElementById('groupModal');
    columnModal = document.getElementById('columnModal');
    borderModal = document.getElementById('borderModal');
    modalTitle = document.getElementById('modalTitle');

    // 表单元素
    taskForm = document.getElementById('taskForm');
    taskId = document.getElementById('taskId');
    taskGroup = document.getElementById('taskGroup');
    taskNumber = document.getElementById('taskNumber');
    taskName = document.getElementById('taskName');
    taskDuration = document.getElementById('taskDuration');
    taskStart = document.getElementById('taskStart');
    taskEnd = document.getElementById('taskEnd');
    taskColor = document.getElementById('taskColor');

    // 分组相关元素
    groupName = document.getElementById('groupName');
    groupList = document.getElementById('groupList');
    addGroupSubmitBtn = document.getElementById('addGroupSubmit');

    // 工具栏按钮
    addTaskBtn = document.getElementById('addTask');
    editTaskBtn = document.getElementById('editTask');
    deleteTaskBtn = document.getElementById('deleteTask');
    copyTaskBtn = document.getElementById('copyTask');
    moveUpBtn = document.getElementById('moveUp');
    moveDownBtn = document.getElementById('moveDown');
    moveLeftBtn = document.getElementById('moveLeft');
    moveRightBtn = document.getElementById('moveRight');
    batchGroupBtn = document.getElementById('batchGroup');
    batchColorBtn = document.getElementById('batchColor');
    groupBtn = document.getElementById('groupBtn');
    columnBtn = document.getElementById('columnBtn');
    borderBtn = document.getElementById('borderBtn');
    exportBtn = document.getElementById('exportBtn');
    importBtn = document.getElementById('importBtn');
}

// 绑定事件监听器
export function bindEventListeners() {
    // 工具栏按钮
    addTaskBtn?.addEventListener('click', openAddTaskModal);
    editTaskBtn?.addEventListener('click', openEditTaskModal);
    deleteTaskBtn?.addEventListener('click', deleteSelectedTasks);
    copyTaskBtn?.addEventListener('click', copySelectedTasks);
    moveUpBtn?.addEventListener('click', () => moveSelectedTasksUp());
    moveDownBtn?.addEventListener('click', () => moveSelectedTasksDown());
    moveLeftBtn?.addEventListener('click', () => moveSelectedTasksLeft());
    moveRightBtn?.addEventListener('click', () => moveSelectedTasksRight());
    batchGroupBtn?.addEventListener('click', openBatchGroupModal);
    batchColorBtn?.addEventListener('click', openBatchColorModal);
    groupBtn?.addEventListener('click', openGroupModal);
    addGroupSubmitBtn?.addEventListener('click', addNewGroup);
    columnBtn?.addEventListener('click', openColumnSettingsModal);
    borderBtn?.addEventListener('click', openBorderSettingsModal);
    exportBtn?.addEventListener('click', exportData);

    // 导入按钮和文件输入
    const importFile = document.getElementById('importFile');
    importBtn?.addEventListener('click', () => importFile?.click());
    importFile?.addEventListener('change', handleFileImport);

    // 表单提交
    taskForm?.addEventListener('submit', saveTask);

    // 模态框关闭
    document.addEventListener('click', handleModalClose);

    // 全选复选框
    const selectAll = document.getElementById('selectAll');
    selectAll?.addEventListener('change', toggleSelectAll);

    // 任务复选框变化（使用事件委托）
    document.addEventListener('change', handleCheckboxChange);

    // 监听任务更新事件
    document.addEventListener('tasksUpdated', updateToolbarButtons);

    // 步长变化
    const customStep = document.getElementById('customStep');
    customStep?.addEventListener('change', () => {
        renderGanttChart();
    });

    // 持续时间变化时自动更新结束日期
    taskDuration?.addEventListener('input', updateEndDate);
    taskStart?.addEventListener('change', updateEndDate);

    // 双击编辑任务条
    document.addEventListener('dblclick', handleDoubleClick);
    // 初始化列宽拖拽（表头）
    initColumnResize();
    // 初始化任务栏总宽度拖拽
    initTasklistWidthResize();
}

// 处理复选框变化
function handleCheckboxChange(e) {
    if (e.target.classList.contains('task-checkbox')) {
        updateToolbarButtons();
        updateSelectAllCheckbox();
    }
}

// 处理双击事件
function handleDoubleClick(e) {
    let taskId = null;

    // 检查是否双击了甘特图任务条
    if (e.target.classList.contains('gantt-task')) {
        taskId = e.target.getAttribute('data-id');
    }
    // 检查是否双击了甘特图单元格
    else if (e.target.classList.contains('gantt-cell')) {
        taskId = e.target.getAttribute('data-task-id');
    }
    // 检查是否双击了表格行
    else if (e.target.closest('tr[data-id]')) {
        taskId = e.target.closest('tr[data-id]').getAttribute('data-id');
    }

    if (taskId) {
        setSelectedTaskId(taskId);
        openEditTaskModal();
    }
}

// 处理模态框关闭
function handleModalClose(e) {
    if (e.target.classList.contains('modal')) {
        closeAllModals();
    }
    if (e.target.classList.contains('close')) {
        closeAllModals();
    }
}

// 更新分组选项
function updateGroupOptions() {
    if (!taskGroup) return;

    taskGroup.innerHTML = '';
    groups.forEach(group => {
        const option = document.createElement('option');
        option.value = group;
        option.textContent = group;
        taskGroup.appendChild(option);
    });
}

// 更新结束日期
function updateEndDate() {
    if (taskStart?.value && taskDuration?.value) {
        const startDate = new Date(taskStart.value);
        const duration = parseInt(taskDuration.value) || 1;
        const endDate = addDays(startDate, duration - 1);
        if (taskEnd) {
            taskEnd.value = formatDateString(endDate);
        }
    }
}

// 打开添加任务模态框
function openAddTaskModal() {
    if (!modalTitle || !taskForm) return;

    modalTitle.textContent = '添加任务';
    taskForm.reset();

    // 设置默认值
    if (taskStart) taskStart.value = new Date().toISOString().split('T')[0];
    if (taskDuration) taskDuration.value = '1';
    if (taskColor) taskColor.value = '#e74c3c';

    updateGroupOptions();
    updateEndDate();

    if (taskModal) taskModal.style.display = 'block';
}

// 打开编辑任务模态框
function openEditTaskModal() {
    const selectedTaskId = getSelectedTaskId();
    if (!selectedTaskId) {
        // 如果没有选中任务，尝试从选中的复选框获取
        const selectedCheckboxes = document.querySelectorAll('.task-checkbox:checked');
        if (selectedCheckboxes.length === 1) {
            setSelectedTaskId(selectedCheckboxes[0].getAttribute('data-id'));
        } else {
            return;
        }
    }

    const task = tasks.find(t => t.id === getSelectedTaskId());
    if (!task || !modalTitle || !taskForm) return;

    modalTitle.textContent = '编辑任务';
    updateGroupOptions();

    // 填充表单数据
    if (taskId) taskId.value = task.id;
    if (taskGroup) taskGroup.value = task.group;
    if (taskNumber) taskNumber.value = task.number;
    if (taskName) taskName.value = task.name;
    if (taskDuration) taskDuration.value = task.duration;
    if (taskStart) taskStart.value = task.startDate;
    if (taskEnd) taskEnd.value = task.endDate;
    if (taskColor) taskColor.value = task.color;

    if (taskModal) taskModal.style.display = 'block';
}

// 关闭任务模态框
function closeTaskModal() {
    if (taskModal) taskModal.style.display = 'none';
}

// 保存任务
function saveTask(e) {
    e.preventDefault();

    const formData = new FormData(taskForm);
    const taskData = {
        group: formData.get('group'),
        number: formData.get('number'),
        name: formData.get('name'),
        duration: parseInt(formData.get('duration')) || 1,
        startDate: formData.get('startDate'),
        endDate: formData.get('endDate'),
        color: formData.get('color')
    };

    const id = formData.get('id');

    if (id) {
        // 编辑现有任务
        updateTask(id, taskData);
    } else {
        // 添加新任务
        addTask(taskData);
    }

    closeTaskModal();
    renderTasks();
    renderGanttChart();
    updateToolbarButtons();
}

// 删除选中的任务
function deleteSelectedTasks() {
    const selectedCheckboxes = document.querySelectorAll('.task-checkbox:checked');
    if (selectedCheckboxes.length === 0) return;

    if (confirm(`确定要删除选中的 ${selectedCheckboxes.length} 个任务吗？`)) {
        const taskIds = Array.from(selectedCheckboxes).map(cb => cb.getAttribute('data-id'));
        deleteTasks(taskIds);

        renderTasks();
        renderGanttChart();
        updateToolbarButtons();
    }
}

// 复制选中的任务
function copySelectedTasks() {
    const selectedCheckboxes = document.querySelectorAll('.task-checkbox:checked');
    if (selectedCheckboxes.length === 0) return;

    const taskIds = Array.from(selectedCheckboxes).map(cb => cb.getAttribute('data-id'));
    copyTasks(taskIds);

    renderTasks();
    renderGanttChart();
    updateToolbarButtons();
}

// 向上移动选中的任务
function moveSelectedTasksUp() {
    const selectedCheckboxes = document.querySelectorAll('.task-checkbox:checked');
    if (selectedCheckboxes.length === 0) return;

    const taskIds = Array.from(selectedCheckboxes).map(cb => cb.getAttribute('data-id'));
    if (moveTasks(taskIds, 'up')) {
        renderTasks();
        renderGanttChart();

        // 保持选中状态
        setTimeout(() => {
            taskIds.forEach(id => {
                const checkbox = document.querySelector(`.task-checkbox[data-id="${id}"]`);
                if (checkbox) checkbox.checked = true;
            });
            updateToolbarButtons();
        }, 50);
    }
}

// 向下移动选中的任务
function moveSelectedTasksDown() {
    const selectedCheckboxes = document.querySelectorAll('.task-checkbox:checked');
    if (selectedCheckboxes.length === 0) return;

    const taskIds = Array.from(selectedCheckboxes).map(cb => cb.getAttribute('data-id'));

    if (moveTasks(taskIds, 'down')) {
        renderTasks();
        renderGanttChart();

        // 保持选中状态
        setTimeout(() => {
            taskIds.forEach(id => {
                const checkbox = document.querySelector(`.task-checkbox[data-id="${id}"]`);
                if (checkbox) checkbox.checked = true;
            });
            updateToolbarButtons();
        }, 50);
    }
}

// 向左移动选中的任务
function moveSelectedTasksLeft() {
    const selectedCheckboxes = document.querySelectorAll('.task-checkbox:checked');
    if (selectedCheckboxes.length === 0) return;

    const moveDaysInput = document.getElementById('moveDaysInput');
    const moveDays = parseInt(moveDaysInput?.value) || 1;

    const taskIds = Array.from(selectedCheckboxes).map(cb => cb.getAttribute('data-id'));
    moveTasksDates(taskIds, -moveDays); // 负数表示向左移动

    renderTasks();
    renderGanttChart();

    // 保持选中状态
    setTimeout(() => {
        taskIds.forEach(id => {
            const checkbox = document.querySelector(`.task-checkbox[data-id="${id}"]`);
            if (checkbox) checkbox.checked = true;
        });
        updateToolbarButtons();
    }, 50);
}

// 向右移动选中的任务
function moveSelectedTasksRight() {
    const selectedCheckboxes = document.querySelectorAll('.task-checkbox:checked');
    if (selectedCheckboxes.length === 0) return;

    const moveDaysInput = document.getElementById('moveDaysInput');
    const moveDays = parseInt(moveDaysInput?.value) || 1;

    const taskIds = Array.from(selectedCheckboxes).map(cb => cb.getAttribute('data-id'));
    moveTasksDates(taskIds, moveDays); // 正数表示向右移动

    renderTasks();
    renderGanttChart();

    // 保持选中状态
    setTimeout(() => {
        taskIds.forEach(id => {
            const checkbox = document.querySelector(`.task-checkbox[data-id="${id}"]`);
            if (checkbox) checkbox.checked = true;
        });
        updateToolbarButtons();
    }, 50);
}

// 更新工具栏按钮状态
export function updateToolbarButtons() {
    const selectedCheckboxes = document.querySelectorAll('.task-checkbox:checked');
    const anySelected = selectedCheckboxes.length > 0;
    const singleSelected = selectedCheckboxes.length === 1;

    // 更新按钮状态
    if (editTaskBtn) editTaskBtn.disabled = !singleSelected;
    if (deleteTaskBtn) deleteTaskBtn.disabled = !anySelected;
    if (copyTaskBtn) copyTaskBtn.disabled = !anySelected;
    if (moveUpBtn) moveUpBtn.disabled = !anySelected;
    if (moveDownBtn) moveDownBtn.disabled = !anySelected;
    if (moveLeftBtn) moveLeftBtn.disabled = !anySelected;
    if (moveRightBtn) moveRightBtn.disabled = !anySelected;
    if (batchGroupBtn) batchGroupBtn.disabled = !anySelected;
    if (batchColorBtn) batchColorBtn.disabled = !anySelected;
}

// 全选/取消全选
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.task-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });

    updateToolbarButtons();
}

// 关闭所有模态框
function closeAllModals() {
    if (taskModal) taskModal.style.display = 'none';
    if (groupModal) groupModal.style.display = 'none';

    // 关闭其他模态框
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        modal.style.display = 'none';
    });
}

// 打开分组管理模态框
function openGroupModal() {
    if (groupName) groupName.value = '';
    renderGroupList();
    if (groupModal) groupModal.style.display = 'block';
}

// 渲染分组列表
function renderGroupList() {
    if (!groupList) return;

    groupList.innerHTML = '';

    groups.forEach(group => {
        const groupItem = document.createElement('div');
        groupItem.className = 'group-item';
        groupItem.innerHTML = `
            <span>${group}</span>
            <button type="button" onclick="deleteGroup('${group}')" ${groups.length <= 1 ? 'disabled' : ''}>删除</button>
        `;
        groupList.appendChild(groupItem);
    });
}

// 添加新分组
function addNewGroup() {
    if (!groupName) return;

    const name = groupName.value.trim();
    if (!name) return;

    if (addGroup(name)) {
        groupName.value = '';
        renderGroupList();
        updateGroupOptions();
        renderTasks(); // 重新渲染任务表格以更新分组选项
    } else {
        alert('分组已存在');
    }
}

// 删除分组
window.deleteGroup = function(groupName) {
    if (deleteGroup(groupName)) {
        renderGroupList();
        updateGroupOptions();
        renderTasks();
    } else {
        alert('至少需要保留一个分组');
    }
};

// 打开批量分组模态框
function openBatchGroupModal() {
    const selectedCheckboxes = document.querySelectorAll('.task-checkbox:checked');
    if (selectedCheckboxes.length === 0) return;

    const modal = document.getElementById('batchGroupModal');
    const select = document.getElementById('batchGroupSelect');

    if (select) {
        select.innerHTML = '';
        groups.forEach(group => {
            const option = document.createElement('option');
            option.value = group;
            option.textContent = group;
            select.appendChild(option);
        });
    }

    if (modal) {
        modal.style.display = 'block';
        // 绑定事件监听器
        bindBatchGroupEvents();
    }
}

// 绑定批量分组事件
function bindBatchGroupEvents() {
    const saveBtn = document.getElementById('saveBatchGroup');
    const cancelBtn = document.getElementById('cancelBatchGroup');

    // 移除旧的事件监听器
    saveBtn?.removeEventListener('click', saveBatchGroup);
    cancelBtn?.removeEventListener('click', cancelBatchGroup);

    // 添加新的事件监听器
    saveBtn?.addEventListener('click', saveBatchGroup);
    cancelBtn?.addEventListener('click', cancelBatchGroup);
}

// 应用批量分组
function saveBatchGroup() {
    const selectedCheckboxes = document.querySelectorAll('.task-checkbox:checked');
    const newGroup = document.getElementById('batchGroupSelect')?.value;

    if (selectedCheckboxes.length === 0 || !newGroup) return;

    const taskIds = Array.from(selectedCheckboxes).map(cb => cb.getAttribute('data-id'));
    updateTasksGroup(taskIds, newGroup);

    const modal = document.getElementById('batchGroupModal');
    if (modal) modal.style.display = 'none';

    renderTasks();
    renderGanttChart();
}

// 取消批量分组
function cancelBatchGroup() {
    const modal = document.getElementById('batchGroupModal');
    if (modal) modal.style.display = 'none';
}

// 打开批量颜色模态框
function openBatchColorModal() {
    const selectedCheckboxes = document.querySelectorAll('.task-checkbox:checked');
    if (selectedCheckboxes.length === 0) return;

    const modal = document.getElementById('batchColorModal');
    if (modal) {
        modal.style.display = 'block';
        // 绑定事件监听器
        bindBatchColorEvents();
    }
}

// 绑定批量改色事件
function bindBatchColorEvents() {
    const applyBtn = document.getElementById('applyBatchColor');
    const cancelBtn = document.getElementById('cancelBatchColor');

    // 移除旧的事件监听器
    applyBtn?.removeEventListener('click', applyBatchColor);
    cancelBtn?.removeEventListener('click', cancelBatchColor);

    // 添加新的事件监听器
    applyBtn?.addEventListener('click', applyBatchColor);
    cancelBtn?.addEventListener('click', cancelBatchColor);
}

// 应用批量颜色
function applyBatchColor() {
    const selectedCheckboxes = document.querySelectorAll('.task-checkbox:checked');
    const newColor = document.getElementById('batchColor')?.value;

    if (selectedCheckboxes.length === 0 || !newColor) return;

    const taskIds = Array.from(selectedCheckboxes).map(cb => cb.getAttribute('data-id'));
    updateTasksColor(taskIds, newColor);

    const modal = document.getElementById('batchColorModal');
    if (modal) modal.style.display = 'none';

    renderTasks();
    renderGanttChart();
}

// 取消批量改色
function cancelBatchColor() {
    const modal = document.getElementById('batchColorModal');
    if (modal) modal.style.display = 'none';
}

// 打开批量移动模态框
function openBatchMoveModal(direction) {
    const selectedCheckboxes = document.querySelectorAll('.task-checkbox:checked');
    if (selectedCheckboxes.length === 0) return;

    const modal = document.getElementById('batchMoveModal');
    const directionSpan = document.getElementById('moveDirection');

    if (directionSpan) {
        directionSpan.textContent = direction === 'left' ? '左移' : '右移';
    }

    // 存储移动方向
    if (modal) {
        modal.setAttribute('data-direction', direction);
        modal.style.display = 'block';
    }
}

// 应用批量移动
window.applyBatchMove = function() {
    const selectedCheckboxes = document.querySelectorAll('.task-checkbox:checked');
    const moveDays = parseInt(document.getElementById('moveDays')?.value) || 1;
    const modal = document.getElementById('batchMoveModal');
    const direction = modal?.getAttribute('data-direction');

    if (selectedCheckboxes.length === 0) return;

    const taskIds = Array.from(selectedCheckboxes).map(cb => cb.getAttribute('data-id'));
    const days = direction === 'left' ? -moveDays : moveDays;

    moveTasksDates(taskIds, days);

    if (modal) modal.style.display = 'none';

    renderTasks();
    renderGanttChart();
};

// 导出数据
function exportData() {
    const exportModal = document.getElementById('exportModal');
    if (exportModal) {
        exportModal.style.display = 'block';
        bindExportEvents();
    }
}

// 绑定导出事件
function bindExportEvents() {
    const exportExcelBtn = document.getElementById('exportExcel');
    const exportJSONBtn = document.getElementById('exportJSON');
    const exportCSVBtn = document.getElementById('exportCSV');
    const cancelExportBtn = document.getElementById('cancelExport');

    // 移除旧的事件监听器
    exportExcelBtn?.removeEventListener('click', exportExcel);
    exportJSONBtn?.removeEventListener('click', exportJSON);
    exportCSVBtn?.removeEventListener('click', exportCSV);
    cancelExportBtn?.removeEventListener('click', cancelExport);

    // 添加新的事件监听器
    exportExcelBtn?.addEventListener('click', exportExcel);
    exportJSONBtn?.addEventListener('click', exportJSON);
    exportCSVBtn?.addEventListener('click', exportCSV);
    cancelExportBtn?.addEventListener('click', cancelExport);
}

// 导出Excel
function exportExcel() {
    import('./import-export.js').then(module => {
        module.exportToExcel();
        closeExportModal();
    }).catch(error => {
        console.error('导出失败:', error);
        alert('导出失败，请重试');
    });
}

// 导出JSON
function exportJSON() {
    import('./import-export.js').then(module => {
        module.exportToJSON();
        closeExportModal();
    }).catch(error => {
        console.error('导出失败:', error);
        alert('导出失败，请重试');
    });
}

// 导出CSV
function exportCSV() {
    import('./import-export.js').then(module => {
        module.exportToCSV();
        closeExportModal();
    }).catch(error => {
        console.error('导出失败:', error);
        alert('导出失败，请重试');
    });
}

// 取消导出
function cancelExport() {
    closeExportModal();
}

// 关闭导出模态框
function closeExportModal() {
    const exportModal = document.getElementById('exportModal');
    if (exportModal) {
        exportModal.style.display = 'none';
    }
}

// 处理文件导入
function handleFileImport(e) {
    const file = e.target.files[0];
    if (!file) return;

    // 使用导入导出模块的通用导入函数
    import('./import-export.js').then(module => {
        module.importData(file);
    }).catch(error => {
        console.error('导入失败:', error);
        alert('导入失败，请重试');
    });

    // 清空文件输入
    e.target.value = '';
}

// 打开列设置模态框
function openColumnSettingsModal() {
    if (columnModal) {
        columnModal.style.display = 'block';

        // 绑定列设置事件
        bindColumnSettingsEvents();
    }
}

// 打开边框设置模态框
function openBorderSettingsModal() {
    if (borderModal) {
        borderModal.style.display = 'block';

        // 绑定边框设置事件
        bindBorderSettingsEvents();
    }
}

// 绑定列设置事件
function bindColumnSettingsEvents() {
    const applyBtn = document.getElementById('saveColumns');
    const cancelBtn = document.getElementById('cancelColumns');

    applyBtn?.addEventListener('click', applyColumnSettings);
    cancelBtn?.addEventListener('click', () => {
        columnModal.style.display = 'none';
    });
}

// 绑定边框设置事件
function bindBorderSettingsEvents() {
    const applyBtn = document.getElementById('applyBorderSettings');
    const resetBtn = document.getElementById('resetBorderSettings');
    const cancelBtn = document.getElementById('cancelBorderSettings');

    // 绑定滑块值显示更新
    const borderWidth = document.getElementById('borderWidth');
    const borderWidthValue = document.getElementById('borderWidthValue');
    const taskHeight = document.getElementById('taskHeight');
    const taskHeightValue = document.getElementById('taskHeightValue');
    const taskOpacity = document.getElementById('taskOpacity');
    const taskOpacityValue = document.getElementById('taskOpacityValue');

    borderWidth?.addEventListener('input', (e) => {
        borderWidthValue.textContent = e.target.value + 'px';
    });

    taskHeight?.addEventListener('input', (e) => {
        taskHeightValue.textContent = e.target.value + 'px';
    });

    taskOpacity?.addEventListener('input', (e) => {
        taskOpacityValue.textContent = e.target.value + '%';
    });

    applyBtn?.addEventListener('click', applyBorderSettings);
    resetBtn?.addEventListener('click', resetBorderSettings);
    cancelBtn?.addEventListener('click', () => {
        borderModal.style.display = 'none';
    });
}

// 应用列设置
function applyColumnSettings() {
    const colGroup = document.getElementById('colGroup').checked;
    const colNumber = document.getElementById('colNumber').checked;
    const colName = document.getElementById('colName').checked;
    const colDuration = document.getElementById('colDuration').checked;
    const colStart = document.getElementById('colStart').checked;
    const colEnd = document.getElementById('colEnd').checked;

    // 获取表头和表格列 - 使用正确的选择器
    const headers = document.querySelectorAll('thead th');
    const taskRows = document.querySelectorAll('#taskTableBody tr');

    // 列索引映射（跳过复选框列和甘特图列）
    const columnMap = {
        group: 1,
        number: 2,
        name: 3,
        duration: 4,
        start: 5,
        end: 6
    };

    // 显示/隐藏表头列
    if (headers[columnMap.group]) headers[columnMap.group].style.display = colGroup ? '' : 'none';
    if (headers[columnMap.number]) headers[columnMap.number].style.display = colNumber ? '' : 'none';
    if (headers[columnMap.name]) headers[columnMap.name].style.display = colName ? '' : 'none';
    if (headers[columnMap.duration]) headers[columnMap.duration].style.display = colDuration ? '' : 'none';
    if (headers[columnMap.start]) headers[columnMap.start].style.display = colStart ? '' : 'none';
    if (headers[columnMap.end]) headers[columnMap.end].style.display = colEnd ? '' : 'none';

    // 显示/隐藏任务表格行中的对应列
    taskRows.forEach(row => {
        const cells = row.querySelectorAll('td');
        if (cells[columnMap.group]) cells[columnMap.group].style.display = colGroup ? '' : 'none';
        if (cells[columnMap.number]) cells[columnMap.number].style.display = colNumber ? '' : 'none';
        if (cells[columnMap.name]) cells[columnMap.name].style.display = colName ? '' : 'none';
        if (cells[columnMap.duration]) cells[columnMap.duration].style.display = colDuration ? '' : 'none';
        if (cells[columnMap.start]) cells[columnMap.start].style.display = colStart ? '' : 'none';
        if (cells[columnMap.end]) cells[columnMap.end].style.display = colEnd ? '' : 'none';
    });

    columnModal.style.display = 'none';
}

// 重置列设置
function resetColumnSettings() {
    document.getElementById('colGroup').checked = true;
    document.getElementById('colNumber').checked = true;
    document.getElementById('colName').checked = true;
    document.getElementById('colDuration').checked = true;
    document.getElementById('colStart').checked = true;
    document.getElementById('colEnd').checked = true;
}

// 应用边框设置
function applyBorderSettings() {
    const borderColor = document.getElementById('borderColor').value;
    const transparentBorder = document.getElementById('transparentBorder').checked;
    const borderWidth = document.getElementById('borderWidth').value;
    const taskHeight = document.getElementById('taskHeight').value;
    const taskOpacity = document.getElementById('taskOpacity').value;

    // 更新CSS变量
    document.documentElement.style.setProperty('--task-bar-height', taskHeight + 'px');

    // 应用边框设置到所有任务条
    const taskBars = document.querySelectorAll('.gantt-task');
    taskBars.forEach(taskBar => {
        if (transparentBorder) {
            taskBar.style.border = 'none';
        } else {
            taskBar.style.border = `${borderWidth}px solid ${borderColor}`;
        }
        taskBar.style.height = taskHeight + 'px';
        taskBar.style.lineHeight = taskHeight + 'px';
        taskBar.style.opacity = taskOpacity / 100;
    });

    // 更新表格行高度
    const tableRows = document.querySelectorAll('#taskTable tbody tr');
    tableRows.forEach(row => {
        row.style.height = taskHeight + 'px';
    });

    borderModal.style.display = 'none';

    // 重新渲染甘特图以应用新设置
    renderGanttChart();
}

// 重置边框设置
function resetBorderSettings() {
    document.getElementById('borderColor').value = '#ffffff';

// 列宽拖拽与任务栏总宽度拖拽实现
function initColumnResize() {
    const headerTable = document.querySelector('.unified-header table');
    if (!headerTable) return;

    const freezeHeaders = headerTable.querySelectorAll('thead th.freeze-col');
    freezeHeaders.forEach((th, index) => {
        // 添加手柄
        let handle = th.querySelector('.resize-handle-col');
        if (!handle) {
            handle = document.createElement('div');
            handle.className = 'resize-handle-col';
            th.style.position = 'relative';
            th.appendChild(handle);
        }

        let startX, startWidth;
        const onMouseDown = (e) => {
            startX = e.clientX;
            startWidth = th.offsetWidth;
            document.addEventListener('mousemove', onMouseMove);
            document.addEventListener('mouseup', onMouseUp);
            e.preventDefault();
        };
        const onMouseMove = (e) => {
            const dx = e.clientX - startX;
            const newWidth = Math.max(40, startWidth + dx);
            // 设置表头与表体对应 col 的宽度
            const colHead = document.querySelector(`#col-h-${th.dataset.colKey}`);
            const colBody = document.querySelector(`#col-b-${th.dataset.colKey}`);
            [colHead, colBody, th].forEach(el => { if (el) el.style.width = newWidth + 'px'; });
            // 同步每一行对应单元格的宽度（通过colgroup已生效，此处主要触发重排）
        };
        const onMouseUp = () => {
            document.removeEventListener('mousemove', onMouseMove);
            document.removeEventListener('mouseup', onMouseUp);
            // 重新计算冻结偏移
            try { window.GanttApp?.refresh?.(); } catch (e) {}
        };
        handle.addEventListener('mousedown', onMouseDown);
    });
}

function initTasklistWidthResize() {
    // 在任务栏右边界添加手柄
    const headerTaskSide = document.querySelector('.task-header');
    if (!headerTaskSide) return;
    let edgeHandle = headerTaskSide.querySelector('.tasklist-resize-handle');
    if (!edgeHandle) {
        edgeHandle = document.createElement('div');
        edgeHandle.className = 'tasklist-resize-handle';
        headerTaskSide.style.position = 'relative';
        headerTaskSide.appendChild(edgeHandle);
    }

    let startX, startWidth;
    const onMouseDown = (e) => {
        startX = e.clientX;
        // 计算当前左侧任务栏总宽 = 前7列宽度合计
        const headCols = ['checkbox','group','number','name','duration','start','end']
          .map(k => document.querySelector(`#col-h-${k}`))
          .filter(Boolean);
        startWidth = headCols.reduce((sum, c) => sum + c.offsetWidth, 0);
        document.addEventListener('mousemove', onMouseMove);
        document.addEventListener('mouseup', onMouseUp);
        e.preventDefault();
    };
    const onMouseMove = (e) => {
        const dx = e.clientX - startX;
        const targetWidth = Math.max(300, startWidth + dx);
        // 计算当前总宽和比例，按比例分配到各列
        const colsKeys = ['checkbox','group','number','name','duration','start','end'];
        const colH = colsKeys.map(k => document.querySelector(`#col-h-${k}`));
        const colB = colsKeys.map(k => document.querySelector(`#col-b-${k}`));
        const currentWidths = colH.map(c => c ? c.offsetWidth : 0);
        const currentTotal = currentWidths.reduce((a,b)=>a+b,0);
        if (!currentTotal) return;
        const scale = targetWidth / currentTotal;
        colH.forEach((c,i)=>{ if(c){ c.style.width = Math.max(40, Math.round(currentWidths[i]*scale)) + 'px'; }});
        colB.forEach((c,i)=>{ if(c){ c.style.width = Math.max(40, Math.round(currentWidths[i]*scale)) + 'px'; }});
    };
    const onMouseUp = () => {
        document.removeEventListener('mousemove', onMouseMove);
        document.removeEventListener('mouseup', onMouseUp);
        try { window.GanttApp?.refresh?.(); } catch (e) {}
    };
    edgeHandle.addEventListener('mousedown', onMouseDown);
}

    document.getElementById('transparentBorder').checked = false;
    document.getElementById('borderWidth').value = '1';
    document.getElementById('taskHeight').value = '50';
    document.getElementById('taskOpacity').value = '100';

    // 更新显示值
    document.getElementById('borderWidthValue').textContent = '1px';
    document.getElementById('taskHeightValue').textContent = '50px';
    document.getElementById('taskOpacityValue').textContent = '100%';
}
