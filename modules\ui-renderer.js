// UI渲染模块
// 负责甘特图、任务表格等界面的渲染和更新

import {
    getDateRange,
    getTimeUnits,
    getMonthBoundaries,
    formatDate,
    getStepDays,
    calculateFixedRowHeight,
    updateHeightRelationship,
    FIXED_ROW_HEIGHT
} from './utils.js';
import { tasks, groups, borderSettings } from './data-manager.js';

// DOM 元素引用
let taskTableBody, ganttChart, timeRuler, customStep, selectAll;

// 初始化DOM元素引用
export function initDOMElements() {
    taskTableBody = document.getElementById('taskTableBody');
    ganttChart = document.getElementById('ganttChart');
    timeRuler = document.getElementById('monthRow'); // 使用月份行作为时间轴引用
    customStep = document.getElementById('customStep');
    selectAll = document.getElementById('selectAll');

    // 调试信息
    console.log('DOM元素初始化结果:', {
        taskTableBody: !!taskTableBody,
        ganttChart: !!ganttChart,
        timeRuler: !!timeRuler,
        customStep: !!customStep,
        selectAll: !!selectAll
    });
}

// 渲染任务表格
export function renderTasks() {
    const taskTableBody = document.getElementById('taskTableBody');
    taskTableBody.innerHTML = '';

    tasks.forEach((task, index) => {
        const row = document.createElement('tr');
        row.setAttribute('data-id', task.id);

        row.innerHTML = `
            <td class="freeze-col freeze-col-1">
                <input type="checkbox" class="task-checkbox" data-id="${task.id}">
            </td>
            <td class="freeze-col freeze-col-2">${task.group}</td>
            <td class="freeze-col freeze-col-3">${task.number}</td>
            <td class="freeze-col freeze-col-4">${task.name}</td>
            <td class="freeze-col freeze-col-5">${task.duration}</td>
            <td class="freeze-col freeze-col-6">${task.startDate}</td>
            <td class="freeze-col freeze-col-7">${task.endDate}</td>
            <td class="gantt-cell" data-task-id="${task.id}">
                <!-- 甘特图任务条将在这里渲染 -->
            </td>
        `;

        taskTableBody.appendChild(row);
    });

    // 更新全选复选框状态
    updateSelectAllCheckbox();

    // 更新冻结列偏移，确保sticky列在渲染后位置正确
    try { applyFreezeOffsets(); } catch (e) { /* 忽略 */ }
}

// 更新全选复选框状态
export function updateSelectAllCheckbox() {
    const checkboxes = document.querySelectorAll('.task-checkbox');
    const checkedCheckboxes = document.querySelectorAll('.task-checkbox:checked');
    const selectAll = document.getElementById('selectAll');

    if (checkboxes.length === 0) {
        selectAll.indeterminate = false;
        selectAll.checked = false;
    } else if (checkedCheckboxes.length === checkboxes.length) {
        selectAll.indeterminate = false;
        selectAll.checked = true;
    } else if (checkedCheckboxes.length > 0) {
        selectAll.indeterminate = true;
        selectAll.checked = false;
    } else {
        selectAll.indeterminate = false;
        selectAll.checked = false;
    }
}

// 渲染甘特图
export function renderGanttChart() {
    const { start, end } = getDateRange(tasks);
    const stepDays = getStepDays();

    // 计算单位宽度 - 自适应容器宽度
    const ganttContainer = document.querySelector('.gantt-chart') || document.querySelector('.unified-panel');
    const containerWidth = ganttContainer ? ganttContainer.clientWidth : 1200; // 默认宽度1200px
    const totalDays = Math.ceil((end - start) / (1000 * 60 * 60 * 24));
    const totalUnits = Math.ceil(totalDays / stepDays);
    const unitWidth = Math.max(50, Math.floor(containerWidth / totalUnits)); // 最小50px，自适应计算

    // 渲染时间标尺
    renderTimeRuler(start, end, stepDays, unitWidth);

    // 渲染任务条
    renderTaskBars(start, end, stepDays, unitWidth);

    // 更新任务表格宽度
    updateTaskTableWidth();

    // 应用边框样式
    applyBorderStyles();

    // 应用冻结列偏移
    applyFreezeOffsets();

    // 修复高度对齐
    fixHeightAlignment();
}

// 计算并设置冻结列的sticky left偏移
function applyFreezeOffsets() {
    const headerTable = document.querySelector('.unified-header table');
    if (!headerTable) return;

    const freezeHeaders = headerTable.querySelectorAll('thead th.freeze-col');
    let accLeft = 0;
    freezeHeaders.forEach((th, idx) => {
        th.style.left = accLeft + 'px';
        const width = th.offsetWidth;
        accLeft += width;
    });

    const rows = document.querySelectorAll('#taskTableBody tr');
    rows.forEach(row => {
        let left = 0;
        const cells = row.querySelectorAll('td.freeze-col');
        cells.forEach((td) => {
            td.style.left = left + 'px';
            const w = td.offsetWidth;
            left += w;
        });
    });
}


// 渲染时间标尺
export function renderTimeRuler(startDate, endDate, stepDays, unitWidth) {
    // 获取月份行和日期行
    const monthRow = document.getElementById('monthRow');
    const dateRow = document.getElementById('dateRow');

    if (!monthRow || !dateRow) return;

    // 清空现有的月份和日期单元格（保留前7个固定列）
    const monthCells = monthRow.querySelectorAll('th:nth-child(n+8)');
    monthCells.forEach(cell => cell.remove());

    const dateCells = dateRow.querySelectorAll('th');
    dateCells.forEach(cell => cell.remove());

    // 获取时间单位（日期格）
    const timeUnits = getTimeUnits(startDate, endDate, stepDays);

    // 渲染日期行
    timeUnits.forEach(unit => {
        const cell = document.createElement('th');
        cell.textContent = unit.label;
        cell.style.width = `${unitWidth}px`;
        cell.style.minWidth = `${unitWidth}px`;
        cell.style.maxWidth = `${unitWidth}px`;
        cell.style.textAlign = 'center';
        // 字体与内边距交由CSS控制，避免内联样式冲突
        cell.style.borderRight = '1px solid #ddd';
        cell.classList.add('time-unit');
        // 添加月份信息作为数据属性，用于计算月份宽度
        cell.setAttribute('data-month', `${unit.date.getFullYear()}-${unit.date.getMonth()}`);

        dateRow.appendChild(cell);
    });

    // 获取月份边界
    const monthBoundaries = getMonthBoundaries(startDate, endDate);

    // 直接根据日期格数量设置月份单元格的 colspan，实现语义化对齐
    // 清空现有的月份单元格（保留前7个固定列）
    const existingMonthCells = monthRow.querySelectorAll('th:nth-child(n+8)');
    existingMonthCells.forEach(cell => cell.remove());

    monthBoundaries.forEach(month => {
        const monthKey = `${month.start.getFullYear()}-${month.start.getMonth()}`;
        const monthDateCells = dateRow.querySelectorAll(`th[data-month="${monthKey}"]`);
        const span = monthDateCells.length;
        if (span === 0) return; // 该月不在显示范围内

        const monthCell = document.createElement('th');
        monthCell.textContent = month.label;
        monthCell.classList.add('month-header');
        monthCell.colSpan = span; // 用 colspan 覆盖该月所有日期格

        monthRow.appendChild(monthCell);
    });
}

// 渲染任务条
export function renderTaskBars(startDate, endDate, stepDays, unitWidth) {
    // 清空隐藏的甘特图容器
    ganttChart.innerHTML = '';

    // 为每个任务在表格的甘特图单元格中创建任务条
    tasks.forEach((task, index) => {
        // 找到对应的表格行
        const taskRow = document.querySelector(`#taskTableBody tr[data-id="${task.id}"]`);
        if (!taskRow) return;

        // 找到该行的甘特图单元格（最后一个单元格）
        const ganttCell = taskRow.querySelector('.gantt-cell');
        if (!ganttCell) return;

        // 清空甘特图单元格
        ganttCell.innerHTML = '';

        // 设置甘特图单元格样式
        ganttCell.style.position = 'relative';
        ganttCell.style.height = '45px';
        ganttCell.style.overflow = 'visible';

        // 在甘特图单元格中创建任务条
        createTaskBarInTableCell(ganttCell, task, index, startDate, endDate, stepDays, unitWidth);
    });
}

// 在表格单元格中创建任务条
function createTaskBarInTableCell(ganttCell, task, index, projectStart, projectEnd, stepDays, unitWidth) {
    const taskStart = new Date(task.startDate);
    const taskEnd = new Date(task.endDate);

    // 计算任务开始位置（相对于项目开始日期的天数）
    const startOffset = Math.floor((taskStart - projectStart) / (1000 * 60 * 60 * 24));
    const taskDuration = Math.ceil((taskEnd - taskStart) / (1000 * 60 * 60 * 24)) + 1;

    // 计算任务条的位置和宽度
    const startUnit = Math.floor(startOffset / stepDays);
    const taskUnits = Math.ceil(taskDuration / stepDays);
    const left = startUnit * unitWidth + (startOffset % stepDays) * (unitWidth / stepDays);
    const width = taskUnits * unitWidth - ((startOffset % stepDays) * (unitWidth / stepDays)) +
                  ((taskDuration % stepDays) * (unitWidth / stepDays));

    // 创建任务条
    const taskBar = document.createElement('div');
    taskBar.className = 'gantt-task';
    taskBar.setAttribute('data-id', task.id);
    taskBar.style.position = 'absolute';
    taskBar.style.left = `${left}px`;
    taskBar.style.width = `${Math.max(width, unitWidth * 0.1)}px`; // 最小宽度
    taskBar.style.height = '35px';
    taskBar.style.top = '5px'; // 垂直居中
    taskBar.style.backgroundColor = task.color;
    taskBar.style.zIndex = '10';
    taskBar.textContent = task.name;

    // 创建左右调整手柄
    const leftHandle = document.createElement('div');
    leftHandle.className = 'resize-handle left-handle';

    const rightHandle = document.createElement('div');
    rightHandle.className = 'resize-handle right-handle';

    taskBar.appendChild(leftHandle);
    taskBar.appendChild(rightHandle);

    // 添加到甘特图单元格
    ganttCell.appendChild(taskBar);

    // 返回任务条元素供后续使用
    return taskBar;
}

// 在单元格中创建任务条（保留原函数以兼容性）
function createTaskBarInCell(ganttRow, task, index, projectStart, projectEnd, stepDays, unitWidth) {
    const taskStart = new Date(task.startDate);
    const taskEnd = new Date(task.endDate);

    // 计算任务开始位置（相对于项目开始日期的天数）
    const startOffset = Math.floor((taskStart - projectStart) / (1000 * 60 * 60 * 24));
    const taskDuration = Math.ceil((taskEnd - taskStart) / (1000 * 60 * 60 * 24)) + 1;

    // 计算任务条的位置和宽度
    const startUnit = Math.floor(startOffset / stepDays);
    const taskUnits = Math.ceil(taskDuration / stepDays);
    const left = startUnit * unitWidth + (startOffset % stepDays) * (unitWidth / stepDays);
    const width = taskUnits * unitWidth - ((startOffset % stepDays) * (unitWidth / stepDays)) +
                  ((taskDuration % stepDays) * (unitWidth / stepDays));

    // 创建任务条
    const taskBar = document.createElement('div');
    taskBar.className = 'gantt-task';
    taskBar.setAttribute('data-id', task.id);
    taskBar.style.left = `${left}px`;
    taskBar.style.width = `${Math.max(width, unitWidth * 0.1)}px`; // 最小宽度
    taskBar.style.backgroundColor = task.color;
    taskBar.textContent = task.name;

    // 创建左右调整手柄
    const leftHandle = document.createElement('div');
    leftHandle.className = 'resize-handle left-handle';

    const rightHandle = document.createElement('div');
    rightHandle.className = 'resize-handle right-handle';

    taskBar.appendChild(leftHandle);
    taskBar.appendChild(rightHandle);

    // 添加到甘特图行
    ganttRow.appendChild(taskBar);

    // 返回任务条元素供后续使用
    return taskBar;
}

// 更新任务表格宽度
export function updateTaskTableWidth() {
    const headers = document.querySelectorAll('.task-header th');
    let totalWidth = 0;

    headers.forEach(header => {
        totalWidth += header.offsetWidth;
    });

    const taskTable = document.querySelector('.task-table');
    if (taskTable) {
        taskTable.style.width = `${totalWidth}px`;
    }

    // 同步甘特图容器宽度
    const ganttContainer = document.querySelector('.gantt-chart') || document.querySelector('.unified-panel');
    if (ganttContainer) {
        const availableWidth = ganttContainer.clientWidth || 1200; // 添加默认值
        // 确保甘特图内容不超出容器宽度
        const ganttContent = document.getElementById('ganttChart');
        if (ganttContent) {
            ganttContent.style.maxWidth = `${availableWidth}px`;
        }
    }
}

// 应用边框样式
export function applyBorderStyles() {
    // 批量获取DOM元素，减少查询次数
    const taskBars = document.querySelectorAll('.gantt-task');
    const taskRows = document.querySelectorAll('#taskTableBody tr');
    const ganttTasks = document.querySelectorAll('.gantt-task');

    if (taskBars.length === 0) return;

    // 计算固定行高
    const { rowHeight, taskBarHeight } = calculateFixedRowHeight(borderSettings);

    // 批量应用样式
    batchApplyStylesWithPrecision(taskRows, ganttTasks, rowHeight);

    console.log(`应用边框样式完成: 行高=${rowHeight}px, 任务条高度=${taskBarHeight}px`);
}

// 批量应用样式（高精度版本）
function batchApplyStylesWithPrecision(taskRows, ganttTasks, fixedRowHeight) {
    const styleUpdates = [];

    // 准备任务行样式更新
    taskRows.forEach((row, index) => {
        styleUpdates.push({
            element: row,
            styles: {
                height: `${fixedRowHeight}px`,
                lineHeight: `${fixedRowHeight}px`
            }
        });
    });

    // 准备甘特图任务条样式更新
    ganttTasks.forEach((task, index) => {
        const taskBarHeight = fixedRowHeight - (borderSettings.borderWidth * 2);
        const topOffset = borderSettings.borderWidth;

        styleUpdates.push({
            element: task,
            styles: {
                height: `${taskBarHeight}px`,
                top: `${topOffset}px`,
                border: borderSettings.transparentBorder ?
                    'none' :
                    `${borderSettings.borderWidth}px solid ${borderSettings.borderColor}`,
                opacity: borderSettings.taskOpacity / 100,
                lineHeight: `${taskBarHeight}px`
            }
        });
    });

    // 批量应用样式更新
    requestAnimationFrame(() => {
        styleUpdates.forEach(update => {
            Object.assign(update.element.style, update.styles);
        });

        // 更新CSS变量
        document.documentElement.style.setProperty('--row-height', `${fixedRowHeight}px`);
        document.documentElement.style.setProperty('--task-bar-height', `${fixedRowHeight - (borderSettings.borderWidth * 2)}px`);

        console.log(`批量样式应用完成: ${styleUpdates.length} 个元素`);
    });
}

// 修复高度对齐
let alignmentAnimationFrame = null;

export function fixHeightAlignment() {
    // 如果已经有动画帧在等待，取消它
    if (alignmentAnimationFrame !== null) {
        cancelAnimationFrame(alignmentAnimationFrame);
    }

    // 使用 requestAnimationFrame 确保在下一次重绘时执行
    alignmentAnimationFrame = requestAnimationFrame(() => {
        const taskTableBody = document.getElementById('taskTableBody');
        const ganttChart = document.getElementById('ganttChart');

        if (!taskTableBody || !ganttChart) {
            alignmentAnimationFrame = null;
            return;
        }

        const taskRows = taskTableBody.querySelectorAll('tr');
        const ganttRows = ganttChart.querySelectorAll('.gantt-row');

        // 计算固定行高
        const { rowHeight } = calculateFixedRowHeight(borderSettings);

        // 同步设置所有行的高度
        taskRows.forEach((row, index) => {
            row.style.height = `${rowHeight}px`;
            row.style.lineHeight = `${rowHeight}px`;
        });

        ganttRows.forEach((row, index) => {
            row.style.height = `${rowHeight}px`;
        });

        // 清除动画帧标记
        alignmentAnimationFrame = null;

        console.log(`高度对齐完成: 行高=${rowHeight}px`);
    });
}

// 更新日期范围显示
export function updateDateRangeDisplay() {
    const startDateDisplay = document.getElementById('startDateDisplay');
    const endDateDisplay = document.getElementById('endDateDisplay');

    if (tasks.length === 0) {
        if (startDateDisplay) startDateDisplay.textContent = '无数据';
        if (endDateDisplay) endDateDisplay.textContent = '无数据';
        return;
    }

    const { start, end } = getDateRange(tasks);

    if (startDateDisplay) {
        startDateDisplay.textContent = start.toLocaleDateString('zh-CN');
    }
    if (endDateDisplay) {
        endDateDisplay.textContent = end.toLocaleDateString('zh-CN');
    }
}

// 调整甘特图主体高度
export function adjustGanttBodyHeight() {
    const unifiedBody = document.querySelector('.unified-body');
    const ganttChart = document.getElementById('ganttChart');

    if (!unifiedBody || !ganttChart) return;

    // 计算需要的高度
    const rowCount = tasks.length;
    const { rowHeight } = calculateFixedRowHeight(borderSettings);
    const totalHeight = rowCount * rowHeight;

    // 设置甘特图高度
    ganttChart.style.height = `${totalHeight}px`;

    // 确保统一主体的高度匹配
    unifiedBody.style.minHeight = `${totalHeight}px`;
}

// 修复滚动对齐
export function fixScrollAlignment() {
    const taskTableBody = document.getElementById('taskTableBody');
    const ganttChart = document.getElementById('ganttChart');

    if (!taskTableBody || !ganttChart) return;

    // 同步滚动位置
    const unifiedBody = document.querySelector('.unified-body');
    if (unifiedBody) {
        taskTableBody.scrollTop = unifiedBody.scrollTop;
        ganttChart.scrollTop = unifiedBody.scrollTop;
    }
}
