# 施工进度甘特图管理系统

这是一个功能完整的甘特图管理系统，专为施工项目进度管理而设计。通过直观的时间线展示，帮助用户更好地规划和跟踪项目进度，提高项目管理效率。

## 🚀 功能特点

### 📋 任务管理
- **任务操作**：添加、编辑、删除任务
- **分组管理**：支持任务分组，便于项目阶段管理
- **批量操作**：批量分组、批量改色、批量移动
- **任务复制**：快速复制任务模板

### 📊 甘特图可视化
- **直观展示**：清晰的时间线和任务条展示
- **任务条颜色**：支持自定义任务条颜色，便于区分不同类型任务
- **时间轴对齐**：精确的时间轴计算，任务条位置准确对应时间
- **自适应高度**：甘特图区域高度自动调整，确保最佳可视化效果

### 🔄 任务移动功能
- **上下移动**：任务排序调整，支持拖拽和按钮操作
- **左右移动**：任务时间调整，支持自定义移动天数（1-365天）
- **保持选中状态**：移动操作后自动保持任务选中状态，便于连续操作
- **批量移动**：支持选中多个任务同时移动

### 📅 时间显示模式
- **多种模式**：支持1天/格、3天/格、5天/格、10天/格和自定义天数/格
- **智能显示**：3天/格模式下显示范围最后一天，确保清晰展示
- **跨月显示**：跨月时自动显示月/日格式，时间对应准确

### 💾 数据管理
- **本地存储**：任务数据自动保存在浏览器本地存储中
- **Excel导入导出**：支持Excel文件导入导出，便于数据交换
- **数据持久化**：刷新页面数据不丢失

### 🎨 界面优化
- **响应式设计**：适配不同屏幕尺寸
- **列设置**：可自定义显示列
- **边框设置**：可调整表格和甘特图边框样式
- **总工期显示**：实时显示项目总工期范围

## 🛠️ 安装使用

### 快速开始

1. **克隆仓库到本地**
   ```bash
   git clone https://gitee.com/better319/horizontal-chart.git
   ```

2. **进入项目目录**
   ```bash
   cd 横道图
   ```

3. **安装依赖**
   ```bash
   npm install
   ```

4. **启动服务器**
   ```bash
   npm start
   ```

5. **访问应用**
   在浏览器中打开 http://127.0.0.1:3005/

### 系统要求

- Node.js 14.0 或更高版本
- 现代浏览器（Chrome、Firefox、Safari、Edge）
- 推荐屏幕分辨率：1920x1080 或更高

## 📁 项目结构

```
施工进度甘特图管理系统/
├── index.html          # 应用入口文件，包含页面结构和基本HTML元素
├── styles.css          # 样式文件，定义应用的视觉风格和响应式布局
├── script.js           # 核心脚本文件，实现所有交互功能和业务逻辑
├── server.js           # Node.js服务器，用于本地开发环境
├── package.json        # 项目配置文件，包含依赖管理和脚本命令
├── package-lock.json   # 依赖版本锁定文件
├── README.md           # 项目说明文档
└── node_modules/       # 依赖包目录
```

### 核心文件说明

- **index.html**：应用入口页面，定义了完整的用户界面结构
- **styles.css**：完整的样式定义，包括甘特图、表格、工具栏等所有组件样式
- **script.js**：核心功能实现，包含：
  - 甘特图渲染引擎
  - 任务管理系统
  - 拖拽交互功能
  - Excel导入导出
  - 本地存储管理
  - 时间轴计算算法

## 📖 使用指南

### 基本操作

#### 任务管理
- **添加任务**：点击"添加任务"按钮，填写任务信息（分组名、编号、工作名称、开始时间、结束时间）
- **编辑任务**：选中任务后点击"编辑任务"按钮，修改任务信息
- **删除任务**：选中任务后点击"删除任务"按钮，确认删除
- **复制任务**：选中任务后点击"复制"按钮，快速创建相似任务

#### 任务移动
- **上下移动**：
  - 选中任务，点击"↑上移"或"↓下移"按钮调整任务顺序
  - 支持拖拽任务行进行排序
- **左右移动**：
  - 选中任务，设置移动天数（1-365天）
  - 点击"向左移动"提前任务时间，点击"向右移动"延后任务时间
  - 移动后任务保持选中状态，便于连续调整

#### 批量操作
- **批量分组**：选中多个任务，统一设置分组名称
- **批量改色**：选中多个任务，统一设置任务条颜色
- **批量移动**：选中多个任务，同时进行时间调整

### 高级功能

#### 时间显示设置
- **标尺步长**：选择不同的时间显示模式
  - 1天/格：精确到天的显示
  - 3天/格：显示每3天范围的最后一天
  - 5天/格、10天/格：适合长期项目
  - 自定义：输入自定义天数/格
- **总工期显示**：页面顶部实时显示项目的开始和结束时间

#### 数据管理
- **Excel导出**：将当前任务数据导出为Excel文件
- **Excel导入**：从Excel文件导入任务数据（支持标准格式）
- **自动保存**：所有操作自动保存到浏览器本地存储

#### 界面定制
- **列设置**：自定义表格显示列
- **边框设置**：调整表格和甘特图的边框样式
- **响应式布局**：自动适配不同屏幕尺寸

## 🔧 技术特性

### 前端技术
- **纯JavaScript**：无框架依赖，轻量级实现
- **HTML5 + CSS3**：现代Web标准
- **Canvas绘图**：高性能甘特图渲染
- **LocalStorage**：本地数据持久化
- **响应式设计**：CSS Grid + Flexbox布局

### 核心算法
- **时间轴计算**：精确的日期范围和位置计算
- **任务条渲染**：高效的甘特图绘制算法
- **拖拽交互**：流畅的用户交互体验
- **数据同步**：表格与甘特图实时同步

### 性能优化
- **虚拟滚动**：大量任务时的性能优化
- **防抖处理**：减少不必要的重绘
- **批量更新**：DOM操作优化
- **内存管理**：避免内存泄漏

## 📋 更新日志

### v2.0.0 (2025-01-08)
- ✨ **新增功能**：
  - 任务左右移动功能，支持自定义移动天数（1-365天）
  - 移动操作后保持任务选中状态，便于连续操作
  - 优化甘特图时间轴对齐，减少不必要的空白区域
  - 完善的工具栏布局，按钮排列更加合理

- 🐛 **问题修复**：
  - 修复任务移动后选中状态丢失的问题
  - 修复甘特图任务条位置偏移问题
  - 修复JavaScript初始化错误
  - 优化时间轴计算逻辑

- 🎨 **界面优化**：
  - 重新设计工具栏布局
  - 添加移动天数输入框
  - 优化按钮排列顺序
  - 改善用户交互体验

### v1.0.0 (2024-12-XX)
- 🎉 **初始版本发布**：
  - 基础甘特图功能
  - 任务管理系统
  - Excel导入导出
  - 多种时间显示模式
  - 响应式界面设计

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进这个项目！

### 开发环境设置
1. Fork本仓库
2. 创建功能分支：`git checkout -b feature/新功能`
3. 提交更改：`git commit -am '添加新功能'`
4. 推送分支：`git push origin feature/新功能`
5. 提交Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 提交Issue：[https://gitee.com/better319/horizontal-chart/issues](https://gitee.com/better319/horizontal-chart/issues)
- 邮箱：[您的邮箱]

---

⭐ 如果这个项目对您有帮助，请给个Star支持一下！