// 工具函数模块
// 包含日期处理、计算函数等通用工具函数

// 固定的行高和任务条高度常量
export const FIXED_ROW_HEIGHT = 45; // 行高
export const HEIGHT_MARGIN = 10; // 行高与任务条高度的固定差值
export const FIXED_TASK_BAR_HEIGHT = FIXED_ROW_HEIGHT - HEIGHT_MARGIN; // 任务条高度 = 行高 - 固定差值

// 获取当前步长天数
export function getStepDays() {
    const customStep = document.getElementById('customStep');
    const stepValue = parseInt(customStep.value) || 1;
    return Math.max(1, stepValue);
}

// 新的统一行高计算函数 - 基于用户需求的固定公式
export function calculateFixedRowHeight(borderSettings) {
    // 修正：行高就是任务条高度，边框是内部的，不影响行高
    const taskHeight = borderSettings.taskHeight || 45;
    const borderWidth = borderSettings.borderWidth || 1;
    const rowHeight = taskHeight; // 行高就是任务条高度
    const taskBarHeight = taskHeight - (borderWidth * 2); // 任务条高度需要减去边框

    console.log(`计算固定行高: 行高=${rowHeight}px, 任务条高度=${taskBarHeight}px (减去边框${borderWidth}px × 2)`);

    return {
        rowHeight: rowHeight,
        taskBarHeight: taskBarHeight
    };
}

// 更新行高和任务条高度的数学关系
export function updateHeightRelationship(newRowHeight, borderSettings) {
    if (newRowHeight && newRowHeight > HEIGHT_MARGIN) {
        // 更新CSS变量
        document.documentElement.style.setProperty('--row-height', `${newRowHeight}px`);
        document.documentElement.style.setProperty('--task-bar-height', `${newRowHeight - HEIGHT_MARGIN}px`);

        // 更新borderSettings以保持一致性
        if (borderSettings) {
            borderSettings.taskHeight = newRowHeight;
        }

        return newRowHeight - HEIGHT_MARGIN;
    }
    return FIXED_TASK_BAR_HEIGHT;
}

// 获取日期范围
export function getDateRange(tasks) {
    if (tasks.length === 0) {
        const today = new Date();
        const start = new Date(today);
        start.setDate(today.getDate() - 7); // 开始日期为今天前7天
        const end = new Date(today);
        end.setDate(today.getDate() + 30); // 结束日期为今天后30天
        return { start, end };
    }

    let minStart = new Date(tasks[0].startDate);
    let maxEnd = new Date(tasks[0].endDate);

    tasks.forEach(task => {
        const taskStart = new Date(task.startDate);
        const taskEnd = new Date(task.endDate);
        if (taskStart < minStart) minStart = taskStart;
        if (taskEnd > maxEnd) maxEnd = taskEnd;
    });

    // 添加一些缓冲时间
    minStart.setDate(minStart.getDate() - 3);
    maxEnd.setDate(maxEnd.getDate() + 3);

    return { start: minStart, end: maxEnd };
}

// 格式化日期
export function formatDate(date, stepDays) {
    const year = date.getFullYear();
    const month = date.getMonth();
    const day = date.getDate();

    if (stepDays >= 30) {
        // 月份显示
        return `${year}-${String(month + 1).padStart(2, '0')}`;
    } else if (stepDays >= 7) {
        // 周显示
        const weekNumber = Math.ceil(day / 7);
        return `${String(month + 1).padStart(2, '0')}-W${weekNumber}`;
    } else {
        // 日显示 - 只显示日期数字
        return String(day);
    }
}

// 获取时间单位
export function getTimeUnits(startDate, endDate, stepDays) {
    const units = [];
    const current = new Date(startDate);
    
    while (current <= endDate) {
        units.push({
            date: new Date(current),
            label: formatDate(current, stepDays)
        });
        current.setDate(current.getDate() + stepDays);
    }
    
    return units;
}

// 获取月份边界
export function getMonthBoundaries(startDate, endDate) {
    const boundaries = [];
    let current = new Date(startDate);

    // 找到第一个月份的开始日期（在显示范围内的实际开始日期）
    const firstMonthStart = new Date(current.getFullYear(), current.getMonth(), 1);

    while (current <= endDate) {
        const monthStart = new Date(current.getFullYear(), current.getMonth(), 1);
        const monthEnd = new Date(current.getFullYear(), current.getMonth() + 1, 0);

        // 计算该月份在显示范围内的实际开始和结束日期
        const actualStart = new Date(Math.max(monthStart.getTime(), startDate.getTime()));
        const actualEnd = new Date(Math.min(monthEnd.getTime(), endDate.getTime()));

        // 只有当该月份在显示范围内有日期时才添加
        if (actualStart <= actualEnd) {
            boundaries.push({
                start: monthStart, // 保持月份的完整信息
                end: monthEnd,     // 保持月份的完整信息
                actualStart: actualStart, // 在显示范围内的实际开始日期
                actualEnd: actualEnd,     // 在显示范围内的实际结束日期
                label: `${current.getFullYear()}年${current.getMonth() + 1}月`
            });
        }

        current.setMonth(current.getMonth() + 1);
    }

    return boundaries;
}

// 获取月份天数
export function getDaysInMonth(year, month) {
    return new Date(year, month + 1, 0).getDate();
}

// 格式化Excel日期
export function formatExcelDate(dateValue) {
    if (!dateValue) return new Date().toISOString().split('T')[0];

    // 如果已经是字符串格式的日期，直接返回
    if (typeof dateValue === 'string' && dateValue.match(/^\d{4}-\d{2}-\d{2}$/)) {
        return dateValue;
    }

    // 如果是Date对象
    if (dateValue instanceof Date) {
        return dateValue.toISOString().split('T')[0];
    }

    // 如果是Excel的数字日期格式
    if (typeof dateValue === 'number') {
        // Excel日期从1900年1月1日开始计算
        // 但Excel错误地认为1900年是闰年，所以需要调整
        const excelEpoch = new Date(1899, 11, 30); // 1899年12月30日
        const date = new Date(excelEpoch.getTime() + dateValue * 24 * 60 * 60 * 1000);
        return date.toISOString().split('T')[0];
    }

    // 尝试解析其他格式
    try {
        const date = new Date(dateValue);
        if (!isNaN(date.getTime())) {
            return date.toISOString().split('T')[0];
        }
    } catch (e) {
        console.warn('无法解析日期:', dateValue);
    }

    // 如果都失败了，返回今天的日期
    return new Date().toISOString().split('T')[0];
}

// 防抖函数
export function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 生成唯一ID
export function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// 深拷贝对象
export function deepClone(obj) {
    if (obj === null || typeof obj !== 'object') return obj;
    if (obj instanceof Date) return new Date(obj);
    if (obj instanceof Array) return obj.map(item => deepClone(item));
    if (typeof obj === 'object') {
        const clonedObj = {};
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                clonedObj[key] = deepClone(obj[key]);
            }
        }
        return clonedObj;
    }
}

// 计算两个日期之间的天数差
export function daysBetween(date1, date2) {
    const oneDay = 24 * 60 * 60 * 1000; // 一天的毫秒数
    const firstDate = new Date(date1);
    const secondDate = new Date(date2);
    return Math.round(Math.abs((firstDate - secondDate) / oneDay));
}

// 添加天数到日期
export function addDays(date, days) {
    const result = new Date(date);
    result.setDate(result.getDate() + days);
    return result;
}

// 格式化日期为YYYY-MM-DD格式
export function formatDateString(date) {
    if (typeof date === 'string') return date;
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}
