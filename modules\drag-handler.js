// 拖拽功能模块
// 负责任务条的拖拽移动、大小调整等交互功能

import { updateTaskDates, tasks, saveTasks } from './data-manager.js';
import { renderTasks, renderGanttChart } from './ui-renderer.js';
import { getStepDays, formatDateString, addDays } from './utils.js';

// 拖拽相关变量
let isDragging = false;
let draggedTask = null;
let draggedTaskId = null;
let dragStartX = 0;
let dragStartY = 0;
let dragOffsetX = 0;
let dragOffsetY = 0;
let dropIndicator = null;
let timeDropIndicator = null;

// 初始化拖拽功能
export function initDragHandlers() {
    // 为所有任务条添加拖拽监听器
    addDragListenersToAllTasks();
    
    // 添加全局鼠标事件监听器
    document.addEventListener('mousemove', handleDragMove);
    document.addEventListener('mouseup', handleDragEnd);
}

// 为所有任务条添加拖拽监听器
export function addDragListenersToAllTasks() {
    const taskBars = document.querySelectorAll('.gantt-task');
    taskBars.forEach((taskBar, index) => {
        const taskId = taskBar.getAttribute('data-id');
        const task = tasks.find(t => t.id === taskId);
        if (task) {
            addDragListeners(taskBar, task, index);
            addResizeListeners(taskBar, task);
            addMoveListeners(taskBar, task);
        }
    });
}

// 添加拖拽监听器
function addDragListeners(taskBar, task, taskIndex) {
    taskBar.addEventListener('mousedown', function(e) {
        e.preventDefault();
        startDrag(e, taskBar, task, taskIndex);
    });
}

// 开始拖拽
function startDrag(e, taskBar, task, taskIndex) {
    // 检查是否点击的是调整手柄
    if (e.target.classList.contains('resize-handle')) {
        return; // 如果是调整手柄，不启动拖拽
    }
    
    isDragging = true;
    draggedTask = taskBar;
    draggedTaskId = task.id;
    
    const rect = taskBar.getBoundingClientRect();
    dragStartX = e.clientX;
    dragStartY = e.clientY;
    dragOffsetX = e.clientX - rect.left;
    dragOffsetY = e.clientY - rect.top;
    
    // 创建拖拽指示器
    createDropIndicators();
    
    // 添加拖拽样式
    taskBar.style.opacity = '0.7';
    taskBar.style.zIndex = '1000';
    
    document.body.style.cursor = 'grabbing';
}

// 处理拖拽移动
function handleDragMove(e) {
    if (!isDragging || !draggedTask) return;
    
    // 更新拖拽指示器
    updateDropIndicators(e);
}

// 处理拖拽结束
function handleDragEnd(e) {
    if (!isDragging || !draggedTask) return;
    
    const deltaX = e.clientX - dragStartX;
    const deltaY = e.clientY - dragStartY;
    
    // 执行拖拽操作
    performDrop(deltaX, deltaY);
    
    // 清理拖拽状态
    cleanupDrag();
}

// 创建拖拽指示器
function createDropIndicators() {
    // 创建行拖拽指示器
    if (!dropIndicator) {
        dropIndicator = document.createElement('div');
        dropIndicator.className = 'drop-indicator';
        dropIndicator.style.cssText = `
            position: absolute;
            height: 2px;
            background-color: #007bff;
            z-index: 1001;
            display: none;
        `;
        document.body.appendChild(dropIndicator);
    }
    
    // 创建时间拖拽指示器
    if (!timeDropIndicator) {
        timeDropIndicator = document.createElement('div');
        timeDropIndicator.className = 'time-drop-indicator';
        timeDropIndicator.style.cssText = `
            position: absolute;
            width: 2px;
            background-color: #28a745;
            z-index: 1001;
            display: none;
        `;
        document.body.appendChild(timeDropIndicator);
    }
}

// 更新拖拽指示器
function updateDropIndicators(e) {
    // 使用任务表格区域而不是隐藏的甘特图容器
    const taskTable = document.getElementById('taskTableBody');
    if (!taskTable) return;

    const tableRect = taskTable.getBoundingClientRect();

    // 检查是否在任务表格区域内
    if (e.clientX >= tableRect.left && e.clientX <= tableRect.right &&
        e.clientY >= tableRect.top && e.clientY <= tableRect.bottom) {

        // 显示时间指示器
        if (timeDropIndicator) {
            timeDropIndicator.style.display = 'block';
            timeDropIndicator.style.left = `${e.clientX}px`;
            timeDropIndicator.style.top = `${tableRect.top}px`;
            timeDropIndicator.style.height = `${tableRect.height}px`;
        }

        // 显示行指示器
        const rowHeight = 45; // 固定行高
        const relativeY = e.clientY - tableRect.top;
        const targetRow = Math.floor(relativeY / rowHeight);

        if (dropIndicator && targetRow >= 0 && targetRow < tasks.length) {
            dropIndicator.style.display = 'block';
            dropIndicator.style.left = `${tableRect.left}px`;
            dropIndicator.style.width = `${tableRect.width}px`;
            dropIndicator.style.top = `${tableRect.top + (targetRow + 1) * rowHeight}px`;
        }
    } else {
        // 隐藏指示器
        if (dropIndicator) dropIndicator.style.display = 'none';
        if (timeDropIndicator) timeDropIndicator.style.display = 'none';
    }
}

// 执行拖拽操作
function performDrop(deltaX, deltaY) {
    const task = tasks.find(t => t.id === draggedTaskId);
    if (!task) return;

    // 使用任务表格区域而不是隐藏的甘特图容器
    const taskTable = document.getElementById('taskTableBody');
    if (!taskTable) return;

    const tableRect = taskTable.getBoundingClientRect();

    // 检查是否为垂直拖拽（Ctrl键按下）
    if (event.ctrlKey) {
        // 垂直拖拽 - 改变任务顺序
        const rowHeight = 45;
        const targetRow = Math.floor((dragStartY + deltaY - tableRect.top) / rowHeight);

        if (targetRow >= 0 && targetRow < tasks.length) {
            const currentIndex = tasks.findIndex(t => t.id === draggedTaskId);
            if (currentIndex !== -1 && currentIndex !== targetRow) {
                // 移动任务到新位置
                const [movedTask] = tasks.splice(currentIndex, 1);
                tasks.splice(targetRow, 0, movedTask);
                saveTasks();

                // 重新渲染
                renderTasks();
                renderGanttChart();

                // 重新初始化拖拽监听器
                setTimeout(() => {
                    addDragListenersToAllTasks();
                }, 100);
            }
        }
    } else {
        // 水平拖拽 - 改变任务时间
        const stepDays = getStepDays();
        const unitWidth = 50; // 假设单位宽度为50px
        const daysMoved = Math.round(deltaX / (unitWidth / stepDays));

        if (daysMoved !== 0) {
            const newStartDate = addDays(new Date(task.startDate), daysMoved);
            const newEndDate = addDays(new Date(task.endDate), daysMoved);

            updateTaskDates(draggedTaskId, newStartDate, newEndDate);

            // 重新渲染
            renderTasks();
            renderGanttChart();

            // 重新初始化拖拽监听器
            setTimeout(() => {
                addDragListenersToAllTasks();
            }, 100);
        }
    }
}

// 清理拖拽状态
function cleanupDrag() {
    isDragging = false;
    draggedTask = null;
    draggedTaskId = null;
    
    // 恢复样式
    const taskBars = document.querySelectorAll('.gantt-task');
    taskBars.forEach(bar => {
        bar.style.opacity = '';
        bar.style.zIndex = '';
    });
    
    document.body.style.cursor = '';
    
    // 隐藏指示器
    if (dropIndicator) dropIndicator.style.display = 'none';
    if (timeDropIndicator) timeDropIndicator.style.display = 'none';
}

// 添加大小调整监听器
function addResizeListeners(taskBar, task) {
    const leftHandle = taskBar.querySelector('.left-handle');
    const rightHandle = taskBar.querySelector('.right-handle');
    
    if (leftHandle) {
        leftHandle.addEventListener('mousedown', (e) => {
            e.stopPropagation();
            startResize(e, task, 'left');
        });
    }
    
    if (rightHandle) {
        rightHandle.addEventListener('mousedown', (e) => {
            e.stopPropagation();
            startResize(e, task, 'right');
        });
    }
}

// 开始大小调整
let isResizing = false;
let resizeType = null;
let resizeTask = null;
let resizeStartX = 0;

function startResize(e, task, type) {
    isResizing = true;
    resizeType = type;
    resizeTask = task;
    resizeStartX = e.clientX;
    
    document.body.style.cursor = 'ew-resize';
    
    // 添加临时事件监听器
    document.addEventListener('mousemove', handleResize);
    document.addEventListener('mouseup', endResize);
}

// 处理大小调整
function handleResize(e) {
    if (!isResizing || !resizeTask) return;
    
    const deltaX = e.clientX - resizeStartX;
    const stepDays = getStepDays();
    const unitWidth = 50; // 假设单位宽度为50px
    const daysDelta = Math.round(deltaX / (unitWidth / stepDays));
    
    if (daysDelta !== 0) {
        const startDate = new Date(resizeTask.startDate);
        const endDate = new Date(resizeTask.endDate);
        
        if (resizeType === 'left') {
            // 调整开始日期
            const newStartDate = addDays(startDate, daysDelta);
            if (newStartDate < endDate) {
                updateTaskDates(resizeTask.id, newStartDate, endDate);
                resizeStartX = e.clientX;
            }
        } else if (resizeType === 'right') {
            // 调整结束日期
            const newEndDate = addDays(endDate, daysDelta);
            if (newEndDate > startDate) {
                updateTaskDates(resizeTask.id, startDate, newEndDate);
                resizeStartX = e.clientX;
            }
        }
        
        // 实时更新显示
        renderTasks();
        renderGanttChart();
        
        // 重新初始化拖拽监听器
        setTimeout(() => {
            addDragListenersToAllTasks();
        }, 50);
    }
}

// 结束大小调整
function endResize() {
    isResizing = false;
    resizeType = null;
    resizeTask = null;
    
    document.body.style.cursor = '';
    
    // 移除临时事件监听器
    document.removeEventListener('mousemove', handleResize);
    document.removeEventListener('mouseup', endResize);
}

// 添加移动监听器（用于任务条的水平移动）
function addMoveListeners(taskBar, task) {
    let isMoveMode = false;
    let moveStartX = 0;
    let originalLeft = 0;
    
    taskBar.addEventListener('mousedown', function(e) {
        // 检查是否是中键点击或者按住Shift键
        if (e.button === 1 || e.shiftKey) {
            e.preventDefault();
            e.stopPropagation();
            
            isMoveMode = true;
            moveStartX = e.clientX;
            originalLeft = parseInt(taskBar.style.left) || 0;
            
            document.body.style.cursor = 'move';
            
            const handleMoveMove = (e) => {
                if (!isMoveMode) return;
                
                const deltaX = e.clientX - moveStartX;
                const stepDays = getStepDays();
                const unitWidth = 50;
                const daysMoved = Math.round(deltaX / (unitWidth / stepDays));
                
                if (daysMoved !== 0) {
                    const newStartDate = addDays(new Date(task.startDate), daysMoved);
                    const newEndDate = addDays(new Date(task.endDate), daysMoved);
                    
                    updateTaskDates(task.id, newStartDate, newEndDate);
                    moveStartX = e.clientX;
                    
                    // 实时更新显示
                    renderTasks();
                    renderGanttChart();
                    
                    // 重新初始化拖拽监听器
                    setTimeout(() => {
                        addDragListenersToAllTasks();
                    }, 50);
                }
            };
            
            const handleMoveEnd = () => {
                isMoveMode = false;
                document.body.style.cursor = '';
                document.removeEventListener('mousemove', handleMoveMove);
                document.removeEventListener('mouseup', handleMoveEnd);
            };
            
            document.addEventListener('mousemove', handleMoveMove);
            document.addEventListener('mouseup', handleMoveEnd);
        }
    });
}

// 清理拖拽指示器
export function cleanupDropIndicators() {
    if (dropIndicator) {
        dropIndicator.remove();
        dropIndicator = null;
    }
    if (timeDropIndicator) {
        timeDropIndicator.remove();
        timeDropIndicator = null;
    }
}
