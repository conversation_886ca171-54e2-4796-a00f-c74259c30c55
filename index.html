<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>施工进度甘特图管理系统</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">

        
        <div class="toolbar">
            <div class="date-range-display" style="display: inline-block; margin-right: 20px; white-space: nowrap;">
                <span style="font-weight: bold; color: #333;">总工期：</span>
                <span id="startDateDisplay" style="color: #e74c3c; font-weight: bold;">--</span>
                <span style="color: #333;">至</span>
                <span id="endDateDisplay" style="color: #e74c3c; font-weight: bold;">--</span>
            </div>
            <button id="addTask" class="btn-primary">添加任务</button>
            <button id="editTask" class="btn-secondary" disabled>编辑任务</button>
            <button id="deleteTask" class="btn-danger" disabled>删除任务</button>
            <button id="groupBtn" class="btn-primary">添加分组</button>
            <button id="batchGroup" class="btn-secondary" disabled title="批量分组">批量分组</button>
            <button id="batchColor" class="btn-secondary" disabled title="批量改色">批量改色</button>
            <button id="moveUp" class="btn-secondary" disabled title="向上移动选中任务">↑上移</button>
            <button id="moveDown" class="btn-secondary" disabled title="向下移动选中任务">↓下移</button>
            <button id="moveLeft" class="btn-secondary" disabled title="向左移动选中任务">左移</button>
            <button id="moveRight" class="btn-secondary" disabled title="向右移动选中任务">右移</button>
            <div class="move-days-container">
                <label for="moveDaysInput">移动天数：</label>
                <input type="number" id="moveDaysInput" value="1" min="1" max="365" title="每次移动的天数">
            </div>
            <button id="copyTask" class="btn-secondary" disabled title="复制选中任务">复制</button>
            <button id="columnBtn" class="btn-secondary">列设置</button>
            <button id="borderBtn" class="btn-secondary">边框设置</button>
            <button id="exportBtn" class="btn-secondary">导出</button>
            <button id="importBtn" class="btn-secondary">导入</button>
            <input type="file" id="importFile" accept=".json,.csv,.xlsx,.xls" style="display: none;">
            <div class="ruler-control" style="white-space: nowrap;">
                <label for="customStep" style="display: inline-block; white-space: nowrap;">步长：</label>
                <input type="number" id="customStep" min="1" value="1" style="display: inline-block; width: 60px; vertical-align: middle;">
                <span style="display: inline-block; white-space: nowrap;">天/格</span>
            </div>
        </div>
        
        <div class="main-content">
            <div class="unified-panel">
                <div class="unified-header">
                    <!-- 统一的表头，包含任务信息和甘特图时间标尺 -->
                    <table style="width: 100%; border-collapse: collapse; table-layout: fixed;">
                        <!-- 定义列宽，确保与任务表格完全匹配 -->
                        <colgroup>
                            <col id="col-h-checkbox" style="width: 40px;">  <!-- 复选框 -->
                            <col id="col-h-group" style="width: 80px;">  <!-- 分组名 -->
                            <col id="col-h-number" style="width: 60px;">  <!-- 编号 -->
                            <col id="col-h-name" style="width: 150px;"> <!-- 工作名称 -->
                            <col id="col-h-duration" style="width: 80px;">  <!-- 持续时间 -->
                            <col id="col-h-start" style="width: 100px;"> <!-- 开始时间 -->
                            <col id="col-h-end" style="width: 100px;"> <!-- 结束时间 -->
                            <col id="col-h-gantt" style="width: auto;">  <!-- 甘特图 -->
                        </colgroup>
                        <thead>
                            <!-- 第一行：任务信息列（跨两行）+ 年月行 -->
                            <tr id="monthRow" style="height: var(--row-height);">
                                <th rowspan="2" class="freeze-col freeze-col-1" data-col-key="checkbox" style="border-right: 1px solid #ddd; vertical-align: middle; font-size: 10px; padding: 2px 4px;"><input type="checkbox" id="selectAll"></th>
                                <th rowspan="2" class="freeze-col freeze-col-2" data-col-key="group" style="border-right: 1px solid #ddd; vertical-align: middle; font-size: 10px; padding: 2px 4px;">分组名</th>
                                <th rowspan="2" class="freeze-col freeze-col-3" data-col-key="number" style="border-right: 1px solid #ddd; vertical-align: middle; font-size: 10px; padding: 2px 4px;">编号</th>
                                <th rowspan="2" class="freeze-col freeze-col-4" data-col-key="name" style="border-right: 1px solid #ddd; vertical-align: middle; font-size: 10px; padding: 2px 4px;">工作名称</th>
                                <th rowspan="2" class="freeze-col freeze-col-5" data-col-key="duration" style="border-right: 1px solid #ddd; vertical-align: middle; font-size: 10px; padding: 2px 4px;">持续时间</th>
                                <th rowspan="2" class="freeze-col freeze-col-6" data-col-key="start" style="border-right: 1px solid #ddd; vertical-align: middle; font-size: 10px; padding: 2px 4px;">开始时间</th>
                                <th rowspan="2" class="freeze-col freeze-col-7" data-col-key="end" style="border-right: 1px solid #ddd; vertical-align: middle; font-size: 10px; padding: 2px 4px;">结束时间</th>
                                <!-- 月份单元格将由JavaScript动态添加 -->
                            </tr>
                            <!-- 第二行：只有甘特图的日期行 -->
                            <tr id="dateRow" style="height: var(--row-height);">
                                <!-- 日期单元格将由JavaScript动态添加 -->
                            </tr>
                        </thead>
                    </table>
                </div>
                <div class="unified-body">
                    <div class="unified-content">
                        <!-- 统一的表格结构，任务信息和甘特图条在同一行 -->
                        <table id="unifiedTaskTable" style="width: 100%; border-collapse: collapse;">
                            <!-- 定义列宽，确保与表头完全匹配 -->
                            <colgroup>
                                <col id="col-b-checkbox" style="width: 40px;">  <!-- 复选框 -->
                                <col id="col-b-group" style="width: 80px;">  <!-- 分组名 -->
                                <col id="col-b-number" style="width: 60px;">  <!-- 编号 -->
                                <col id="col-b-name" style="width: 150px;"> <!-- 工作名称 -->
                                <col id="col-b-duration" style="width: 80px;">  <!-- 持续时间 -->
                                <col id="col-b-start" style="width: 100px;"> <!-- 开始时间 -->
                                <col id="col-b-end" style="width: 100px;"> <!-- 结束时间 -->
                                <col id="col-b-gantt" style="width: auto;">  <!-- 甘特图 -->
                            </colgroup>
                            <tbody id="taskTableBody">
                                <!-- 任务行将在这里生成，每行包含任务信息和甘特图条 -->
                            </tbody>
                        </table>

                        <!-- 保留原有的甘特图容器作为备用 -->
                        <div class="gantt-chart" id="ganttChartContainer" style="display: none;">
                            <div id="ganttChart"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Task Modal -->
    <div id="taskModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2 id="modalTitle">添加任务</h2>
            <form id="taskForm">
                <input type="hidden" id="taskId" name="id">
                <div class="form-group">
                    <label for="taskGroup">分组名：</label>
                    <select id="taskGroup" name="group" required>
                        <option value="前期">前期</option>
                        <option value="幕墙施工">幕墙施工</option>
                        <option value="后期">后期</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="taskName">工作名称：</label>
                    <input type="text" id="taskName" name="name" required>
                </div>
                <div class="form-group">
                    <label for="taskDuration">持续时间（天）：</label>
                    <input type="number" id="taskDuration" name="duration" min="1" required>
                </div>
                <div class="form-group">
                    <label for="taskStart">开始时间：</label>
                    <input type="date" id="taskStart" name="startDate" required>
                </div>
                <div class="form-group">
                    <label for="taskEnd">结束时间：</label>
                    <input type="date" id="taskEnd" name="endDate" required readonly>
                </div>
                <div class="form-group">
                    <label for="taskColor">任务颜色：</label>
                    <input type="color" id="taskColor" name="color" value="#e74c3c">
                </div>
                <div class="form-actions">
                    <button type="submit" class="btn-primary">保存</button>
                    <button type="button" class="btn-secondary" id="cancelTask">取消</button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Group Modal -->
    <div id="groupModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>分组管理</h2>
            <div class="form-group">
                <label for="groupName">分组名称：</label>
                <input type="text" id="groupName" placeholder="输入新分组名称">
                <button id="addGroupSubmit" class="btn-primary">添加分组</button>
            </div>
            <div class="group-list">
                <h3>现有分组：</h3>
                <ul id="groupList">
                    <!-- Group list will be generated here -->
                </ul>
            </div>
        </div>
    </div>
    
    <!-- Batch Group Modal -->
    <div id="batchGroupModal" class="modal">
        <div class="modal-content" style="max-width: 400px;">
            <span class="close">&times;</span>
            <h2 style="margin-bottom: 20px; color: #2c3e50;">批量分组</h2>
            <div class="form-group" style="margin-bottom: 20px;">
                <label for="batchGroupSelect" style="font-size: 16px; font-weight: 500; color: #333; margin-bottom: 10px; display: block;">选择分组：</label>
                <select id="batchGroupSelect" class="form-select" style="width: 100%; padding: 12px; font-size: 16px; border: 1px solid #ddd; border-radius: 4px;">
                    <!-- 分组选项将动态加载 -->
                </select>
            </div>
            <div class="form-actions" style="display: flex; gap: 10px; justify-content: flex-end;">
                <button type="button" class="btn-primary" id="saveBatchGroup">确定</button>
                <button type="button" class="btn-secondary" id="cancelBatchGroup">取消</button>
            </div>
        </div>
    </div>
    
    <!-- Border Settings Modal -->
    <div id="borderModal" class="modal">
        <div class="modal-content" style="max-width: 500px;">
            <span class="close">×</span>
            <h2 style="margin-bottom: 20px; color: #2c3e50;">任务条边框设置</h2>
            <div class="form-group" style="margin-bottom: 15px;">
                <label for="borderColor">边框颜色：</label>
                <input type="color" id="borderColor" value="#ffffff" style="width: 50px; height: 30px;">
                <label style="margin-left: 20px;">
                    <input type="checkbox" id="transparentBorder" style="margin-right: 5px;"> 全透明
                </label>
            </div>
            <div class="form-group" style="margin-bottom: 15px;">
                <label for="borderWidth">边框宽度：</label>
                <input type="range" id="borderWidth" min="0" max="5" value="1" style="width: 200px;">
                <span id="borderWidthValue">1px</span>
            </div>
            <div class="form-group" style="margin-bottom: 15px;">
                <label for="taskHeight">任务条高度：</label>
                <input type="range" id="taskHeight" min="30" max="60" value="50" style="width: 200px;">
                <span id="taskHeightValue">50px</span>
            </div>
            <div class="form-group" style="margin-bottom: 20px;">
                <label for="taskOpacity">任务条透明度：</label>
                <input type="range" id="taskOpacity" min="0" max="100" value="100" style="width: 200px;">
                <span id="taskOpacityValue">100%</span>
            </div>
            <div class="form-actions" style="display: flex; gap: 10px; justify-content: flex-end;">
                <button type="button" class="btn-primary" id="applyBorderSettings">应用</button>
                <button type="button" class="btn-secondary" id="resetBorderSettings">重置</button>
                <button type="button" class="btn-secondary" id="cancelBorderSettings">取消</button>
            </div>
        </div>
    </div>

    <!-- Column Settings Modal -->
    <div id="columnModal" class="modal">
        <div class="modal-content" style="max-width: 400px;">
            <span class="close">&times;</span>
            <h2 style="margin-bottom: 20px; color: #2c3e50;">列显示设置</h2>
            <div class="form-group" style="display: flex; flex-direction: column; gap: 15px; margin-bottom: 20px;">
                <div class="checkbox-item" style="display: flex; align-items: center; gap: 15px; padding: 10px; border-radius: 5px; background: #f8f9fa; cursor: pointer; transition: all 0.3s;">
                    <input type="checkbox" id="colGroup" checked style="width: 20px; height: 20px; cursor: pointer; accent-color: #3498db;">
                    <label for="colGroup" style="cursor: pointer; font-size: 16px; font-weight: 500; color: #333;">分组名</label>
                </div>
                <div class="checkbox-item" style="display: flex; align-items: center; gap: 15px; padding: 10px; border-radius: 5px; background: #f8f9fa; cursor: pointer; transition: all 0.3s;">
                    <input type="checkbox" id="colNumber" checked style="width: 20px; height: 20px; cursor: pointer; accent-color: #3498db;">
                    <label for="colNumber" style="cursor: pointer; font-size: 16px; font-weight: 500; color: #333;">编号</label>
                </div>
                <div class="checkbox-item" style="display: flex; align-items: center; gap: 15px; padding: 10px; border-radius: 5px; background: #f8f9fa; cursor: pointer; transition: all 0.3s;">
                    <input type="checkbox" id="colName" checked style="width: 20px; height: 20px; cursor: pointer; accent-color: #3498db;">
                    <label for="colName" style="cursor: pointer; font-size: 16px; font-weight: 500; color: #333;">工作名称</label>
                </div>
                <div class="checkbox-item" style="display: flex; align-items: center; gap: 15px; padding: 10px; border-radius: 5px; background: #f8f9fa; cursor: pointer; transition: all 0.3s;">
                    <input type="checkbox" id="colDuration" checked style="width: 20px; height: 20px; cursor: pointer; accent-color: #3498db;">
                    <label for="colDuration" style="cursor: pointer; font-size: 16px; font-weight: 500; color: #333;">持续时间</label>
                </div>
                <div class="checkbox-item" style="display: flex; align-items: center; gap: 15px; padding: 10px; border-radius: 5px; background: #f8f9fa; cursor: pointer; transition: all 0.3s;">
                    <input type="checkbox" id="colStart" checked style="width: 20px; height: 20px; cursor: pointer; accent-color: #3498db;">
                    <label for="colStart" style="cursor: pointer; font-size: 16px; font-weight: 500; color: #333;">开始时间</label>
                </div>
                <div class="checkbox-item" style="display: flex; align-items: center; gap: 15px; padding: 10px; border-radius: 5px; background: #f8f9fa; cursor: pointer; transition: all 0.3s;">
                    <input type="checkbox" id="colEnd" checked style="width: 20px; height: 20px; cursor: pointer; accent-color: #3498db;">
                    <label for="colEnd" style="cursor: pointer; font-size: 16px; font-weight: 500; color: #333;">结束时间</label>
                </div>
            </div>
            <div class="form-actions" style="display: flex; gap: 10px; justify-content: flex-end;">
                <button type="button" class="btn-primary" id="saveColumns">保存设置</button>
                <button type="button" class="btn-secondary" id="cancelColumns">取消</button>
            </div>
        </div>
    </div>

    <!-- 批量颜色选择模态框 -->
    <div id="batchColorModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h3>批量改色</h3>
            <div class="form-group">
                <label for="batchColor">选择颜色：</label>
                <input type="color" id="batchColor" value="#e74c3c">
            </div>
            <div class="form-actions" style="display: flex; gap: 10px; justify-content: flex-end;">
                <button type="button" class="btn-primary" id="applyBatchColor">应用</button>
                <button type="button" class="btn-secondary" id="cancelBatchColor">取消</button>
            </div>
        </div>
    </div>

    <!-- 批量移动模态框 -->
    <div id="batchMoveModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h3 id="batchMoveTitle">批量移动任务</h3>
            <div class="form-group">
                <label for="moveDays">移动天数：</label>
                <input type="number" id="moveDays" value="1" min="1" max="365">
                <span>天</span>
            </div>
            <div class="form-actions" style="display: flex; gap: 10px; justify-content: flex-end;">
                <button type="button" class="btn-primary" id="applyBatchMove">应用</button>
                <button type="button" class="btn-secondary" id="cancelBatchMove">取消</button>
            </div>
        </div>
    </div>

    <!-- 导出格式选择模态框 -->
    <div id="exportModal" class="modal">
        <div class="modal-content">
            <h3>选择导出格式</h3>
            <div class="form-group">
                <button id="exportExcel" class="btn-primary">导出Excel文件</button>
                <button id="exportJSON" class="btn-secondary">导出JSON文件</button>
                <button id="exportCSV" class="btn-secondary">导出CSV文件</button>
            </div>
            <div class="form-actions">
                <button type="button" id="cancelExport" class="btn-secondary">取消</button>
            </div>
        </div>
    </div>

    <!-- 垂直拖拽提示 -->
    <div id="verticalDragHint" class="vertical-drag-hint">
        按住Ctrl键并拖拽任务条可上下移动
    </div>

    <script type="module" src="modules/main.js"></script>
</body>
</html>