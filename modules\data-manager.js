// 数据管理模块
// 负责任务数据的增删改查和本地存储管理

import { generateId, formatDateString, addDays } from './utils.js';

// 数据存储
export let tasks = JSON.parse(localStorage.getItem('ganttTasks')) || [];
export let groups = JSON.parse(localStorage.getItem('ganttGroups')) || ['前期', '幕墙施工', '后期'];
export let selectedTaskId = null;

// 边框设置相关变量
export let borderSettings = JSON.parse(localStorage.getItem('ganttBorderSettings')) || {
    borderColor: '#ffffff',
    borderWidth: 1,
    taskHeight: 45, // 固定行高45px
    taskOpacity: 100,
    transparentBorder: false
};

// 确保所有任务都有颜色属性和编号
if (tasks.length > 0) {
    let needsSave = false;

    // 检查并添加颜色属性
    if (!tasks[0].color) {
        tasks = tasks.map(task => ({
            ...task,
            color: task.color || '#e74c3c' // 默认颜色
        }));
        needsSave = true;
    }

    // 检查并添加编号属性
    tasks.forEach((task, index) => {
        if (!task.number || task.number === '') {
            task.number = (index + 1).toString().padStart(3, '0');
            needsSave = true;
        }
    });

    if (needsSave) {
        saveTasks();
    }
}

// 保存任务到本地存储
export function saveTasks() {
    localStorage.setItem('ganttTasks', JSON.stringify(tasks));
}

// 保存分组到本地存储
export function saveGroups() {
    localStorage.setItem('ganttGroups', JSON.stringify(groups));
}

// 保存边框设置到本地存储
export function saveBorderSettings() {
    localStorage.setItem('ganttBorderSettings', JSON.stringify(borderSettings));
}

// 生成下一个任务编号
function generateNextTaskNumber() {
    if (tasks.length === 0) return '001';

    // 获取所有现有编号
    const existingNumbers = tasks
        .map(task => task.number)
        .filter(num => num && /^\d{3}$/.test(num)) // 只考虑三位数字格式的编号
        .map(num => parseInt(num))
        .sort((a, b) => a - b);

    // 找到下一个可用编号
    let nextNumber = 1;
    for (const num of existingNumbers) {
        if (num === nextNumber) {
            nextNumber++;
        } else {
            break;
        }
    }

    return nextNumber.toString().padStart(3, '0');
}

// 添加任务
export function addTask(taskData) {
    const newTask = {
        id: generateId(),
        group: taskData.group || groups[0],
        number: taskData.number || generateNextTaskNumber(),
        name: taskData.name || '',
        duration: parseInt(taskData.duration) || 1,
        startDate: taskData.startDate || new Date().toISOString().split('T')[0],
        endDate: taskData.endDate || addDays(new Date(), 1).toISOString().split('T')[0],
        color: taskData.color || '#e74c3c'
    };

    tasks.push(newTask);
    saveTasks();
    return newTask;
}

// 更新任务
export function updateTask(taskId, taskData) {
    const taskIndex = tasks.findIndex(t => t.id === taskId);
    if (taskIndex === -1) return null;
    
    tasks[taskIndex] = {
        ...tasks[taskIndex],
        ...taskData,
        id: taskId // 确保ID不被覆盖
    };
    
    saveTasks();
    return tasks[taskIndex];
}

// 删除任务
export function deleteTask(taskId) {
    const taskIndex = tasks.findIndex(t => t.id === taskId);
    if (taskIndex === -1) return false;
    
    tasks.splice(taskIndex, 1);
    saveTasks();
    return true;
}

// 删除多个任务
export function deleteTasks(taskIds) {
    const idsToDelete = new Set(taskIds);
    tasks = tasks.filter(task => !idsToDelete.has(task.id));
    saveTasks();
}

// 获取任务
export function getTask(taskId) {
    return tasks.find(t => t.id === taskId);
}

// 获取所有任务
export function getAllTasks() {
    return [...tasks];
}

// 复制任务
export function copyTask(taskId) {
    const originalTask = getTask(taskId);
    if (!originalTask) return null;

    const copiedTask = {
        ...originalTask,
        id: generateId(),
        name: originalTask.name + ' (副本)',
        number: generateNextTaskNumber()
    };

    tasks.push(copiedTask);
    saveTasks();
    return copiedTask;
}

// 复制多个任务
export function copyTasks(taskIds) {
    const copiedTasks = [];
    taskIds.forEach(taskId => {
        const copiedTask = copyTask(taskId);
        if (copiedTask) {
            copiedTasks.push(copiedTask);
        }
    });
    return copiedTasks;
}

// 移动任务位置
export function moveTask(taskId, direction) {
    const taskIndex = tasks.findIndex(t => t.id === taskId);
    if (taskIndex === -1) return false;
    
    const newIndex = direction === 'up' ? taskIndex - 1 : taskIndex + 1;
    if (newIndex < 0 || newIndex >= tasks.length) return false;
    
    // 交换任务位置
    [tasks[taskIndex], tasks[newIndex]] = [tasks[newIndex], tasks[taskIndex]];
    saveTasks();
    return true;
}

// 批量移动任务位置
export function moveTasks(taskIds, direction) {
    if (taskIds.length === 0) return false;

    const selectedTasks = tasks.filter(task => taskIds.includes(task.id));

    if (direction === 'up') {
        // 向上移动：找到第一个选中任务的位置
        const firstSelectedIndex = tasks.findIndex(task => taskIds.includes(task.id));
        if (firstSelectedIndex === 0) return false; // 已经在最顶部

        // 简单的向上移动：与前一个任务交换位置
        const newTasks = [...tasks];
        const targetIndex = firstSelectedIndex - 1;

        // 移除选中的任务
        for (let i = newTasks.length - 1; i >= 0; i--) {
            if (taskIds.includes(newTasks[i].id)) {
                newTasks.splice(i, 1);
            }
        }

        // 在目标位置插入选中的任务
        newTasks.splice(targetIndex, 0, ...selectedTasks);

        tasks.length = 0;
        tasks.push(...newTasks);
    } else {
        // 向下移动：找到最后一个选中任务的位置
        let lastSelectedIndex = -1;
        for (let i = tasks.length - 1; i >= 0; i--) {
            if (taskIds.includes(tasks[i].id)) {
                lastSelectedIndex = i;
                break;
            }
        }
        if (lastSelectedIndex === tasks.length - 1) {
            return false; // 已经在最底部
        }

        // 简化的向下移动逻辑：直接交换位置
        const newTasks = [...tasks];

        // 对于单个任务的下移，直接与下一个任务交换位置
        if (taskIds.length === 1) {
            const taskIndex = newTasks.findIndex(t => t.id === taskIds[0]);
            if (taskIndex < newTasks.length - 1) {
                // 交换当前任务和下一个任务的位置
                [newTasks[taskIndex], newTasks[taskIndex + 1]] = [newTasks[taskIndex + 1], newTasks[taskIndex]];
            }
        } else {
            // 多个任务的移动逻辑（保留原有复杂逻辑）
            let targetIndex = lastSelectedIndex + 1;

            // 找到下一个非选中任务的位置
            while (targetIndex < newTasks.length && taskIds.includes(newTasks[targetIndex].id)) {
                targetIndex++;
            }

            // 移除选中的任务
            for (let i = newTasks.length - 1; i >= 0; i--) {
                if (taskIds.includes(newTasks[i].id)) {
                    newTasks.splice(i, 1);
                    if (i < targetIndex) targetIndex--;
                }
            }

            // 在目标位置插入选中的任务
            newTasks.splice(targetIndex, 0, ...selectedTasks);
        }

        tasks.length = 0;
        tasks.push(...newTasks);
    }

    saveTasks();
    return true;
}

// 更新任务日期
export function updateTaskDates(taskId, newStartDate, newEndDate) {
    const taskIndex = tasks.findIndex(t => t.id === taskId);
    if (taskIndex === -1) return false;
    
    const startDate = new Date(newStartDate);
    const endDate = new Date(newEndDate);
    const duration = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24)) + 1;
    
    tasks[taskIndex].startDate = formatDateString(newStartDate);
    tasks[taskIndex].endDate = formatDateString(newEndDate);
    tasks[taskIndex].duration = duration;
    
    saveTasks();
    return true;
}

// 批量更新任务分组
export function updateTasksGroup(taskIds, newGroup) {
    let updated = false;
    taskIds.forEach(taskId => {
        const taskIndex = tasks.findIndex(t => t.id === taskId);
        if (taskIndex !== -1) {
            tasks[taskIndex].group = newGroup;
            updated = true;
        }
    });
    
    if (updated) {
        saveTasks();
    }
    return updated;
}

// 批量更新任务颜色
export function updateTasksColor(taskIds, newColor) {
    let updated = false;
    taskIds.forEach(taskId => {
        const taskIndex = tasks.findIndex(t => t.id === taskId);
        if (taskIndex !== -1) {
            tasks[taskIndex].color = newColor;
            updated = true;
        }
    });
    
    if (updated) {
        saveTasks();
    }
    return updated;
}

// 批量移动任务日期
export function moveTasksDates(taskIds, days) {
    let updated = false;
    taskIds.forEach(taskId => {
        const taskIndex = tasks.findIndex(t => t.id === taskId);
        if (taskIndex !== -1) {
            const task = tasks[taskIndex];
            const newStartDate = addDays(new Date(task.startDate), days);
            const newEndDate = addDays(new Date(task.endDate), days);
            
            task.startDate = formatDateString(newStartDate);
            task.endDate = formatDateString(newEndDate);
            updated = true;
        }
    });
    
    if (updated) {
        saveTasks();
    }
    return updated;
}

// 添加分组
export function addGroup(groupName) {
    if (!groups.includes(groupName)) {
        groups.push(groupName);
        saveGroups();
        return true;
    }
    return false;
}

// 删除分组
export function deleteGroup(groupName) {
    if (groups.length <= 1) return false; // 至少保留一个分组
    
    const index = groups.indexOf(groupName);
    if (index === -1) return false;
    
    // 将使用该分组的任务移动到第一个分组
    const remainingGroups = groups.filter(g => g !== groupName);
    const defaultGroup = remainingGroups[0];
    
    tasks.forEach(task => {
        if (task.group === groupName) {
            task.group = defaultGroup;
        }
    });
    
    groups.splice(index, 1);
    saveGroups();
    saveTasks();
    return true;
}

// 设置选中的任务ID
export function setSelectedTaskId(taskId) {
    selectedTaskId = taskId;
}

// 获取选中的任务ID
export function getSelectedTaskId() {
    return selectedTaskId;
}

// 清空所有数据
export function clearAllData() {
    tasks = [];
    selectedTaskId = null;
    saveTasks();
}

// 导入任务数据
export function importTasks(importedTasks) {
    if (!Array.isArray(importedTasks)) return false;

    // 验证和清理导入的数据
    const validTasks = importedTasks.filter(task => {
        return task.name && task.startDate && task.endDate;
    }).map(task => ({
        id: task.id || generateId(),
        group: task.group || groups[0],
        number: task.number || '',
        name: task.name,
        duration: parseInt(task.duration) || 1,
        startDate: formatDateString(task.startDate),
        endDate: formatDateString(task.endDate),
        color: task.color || '#e74c3c'
    }));

    tasks.length = 0;
    tasks.push(...validTasks);
    saveTasks();
    return true;
}

// 添加示例数据
export function addSampleData() {
    if (tasks.length === 0) {
        // 获取当前日期作为项目开始日期
        const today = new Date();
        const startDate = new Date(today);
        startDate.setDate(today.getDate() + 1); // 从明天开始

        const sampleTasks = [
            {
                id: generateId(),
                group: '前期',
                number: '001',
                name: '需求分析',
                duration: 10,
                startDate: formatDateString(startDate),
                endDate: formatDateString(addDays(startDate, 9)),
                color: '#3498db'
            },
            {
                id: generateId(),
                group: '前期',
                number: '002',
                name: '设计方案',
                duration: 15,
                startDate: formatDateString(addDays(startDate, 10)),
                endDate: formatDateString(addDays(startDate, 24)),
                color: '#2ecc71'
            },
            {
                id: generateId(),
                group: '幕墙施工',
                number: '003',
                name: '材料采购',
                duration: 7,
                startDate: formatDateString(addDays(startDate, 25)),
                endDate: formatDateString(addDays(startDate, 31)),
                color: '#e74c3c'
            },
            {
                id: generateId(),
                group: '幕墙施工',
                number: '004',
                name: '现场施工',
                duration: 30,
                startDate: formatDateString(addDays(startDate, 32)),
                endDate: formatDateString(addDays(startDate, 61)),
                color: '#f39c12'
            },
            {
                id: generateId(),
                group: '后期',
                number: '005',
                name: '质量检测',
                duration: 5,
                startDate: formatDateString(addDays(startDate, 62)),
                endDate: formatDateString(addDays(startDate, 66)),
                color: '#9b59b6'
            },
            {
                id: generateId(),
                group: '后期',
                number: '006',
                name: '项目验收',
                duration: 3,
                startDate: formatDateString(addDays(startDate, 67)),
                endDate: formatDateString(addDays(startDate, 69)),
                color: '#1abc9c'
            }
        ];

        tasks.push(...sampleTasks);
        saveTasks();
        return true;
    }
    return false;
}
