// 导入导出模块
// 负责数据的导入导出功能，支持JSON、CSV等格式

import { tasks, groups, importTasks, saveTasks, saveGroups } from './data-manager.js';
import { renderTasks, renderGanttChart } from './ui-renderer.js';
import { formatExcelDate, generateId } from './utils.js';

// 导出为JSON格式
export function exportToJSON() {
    if (tasks.length === 0) {
        alert('没有可导出的任务数据');
        return;
    }
    
    const data = {
        tasks: tasks,
        groups: groups,
        exportDate: new Date().toISOString(),
        version: '1.0'
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `gantt-data-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

// 导出为CSV格式
export function exportToCSV() {
    if (tasks.length === 0) {
        alert('没有可导出的任务数据');
        return;
    }
    
    // CSV头部
    const headers = ['序号', '分组名', '工作编号', '工作名称', '持续时间', '开始时间', '结束时间', '任务颜色'];
    
    // CSV数据行
    const rows = tasks.map((task, index) => [
        index + 1,
        task.group,
        task.number,
        task.name,
        task.duration,
        task.startDate,
        task.endDate,
        task.color
    ]);
    
    // 组合CSV内容
    const csvContent = [headers, ...rows]
        .map(row => row.map(field => `"${field}"`).join(','))
        .join('\n');
    
    // 添加BOM以支持中文
    const bom = '\uFEFF';
    const blob = new Blob([bom + csvContent], { type: 'text/csv;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `gantt-data-${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

// 导出为Excel格式（使用HTML表格格式，Excel可以打开）
export function exportToExcel() {
    if (tasks.length === 0) {
        alert('没有可导出的任务数据');
        return;
    }

    // 创建HTML表格格式的Excel文件
    let htmlContent = `
        <html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40">
        <head>
            <meta charset="UTF-8">
            <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
            <!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>甘特图数据</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]-->
        </head>
        <body>
            <table border="1">
                <thead>
                    <tr>
                        <th>分组名</th>
                        <th>编号</th>
                        <th>工作名称</th>
                        <th>持续时间</th>
                        <th>开始时间</th>
                        <th>结束时间</th>
                    </tr>
                </thead>
                <tbody>`;

    // 添加数据行
    tasks.forEach((task, index) => {
        htmlContent += `
                    <tr>
                        <td>${task.group}</td>
                        <td>${task.number}</td>
                        <td>${task.name}</td>
                        <td>${task.duration}</td>
                        <td>${task.startDate}</td>
                        <td>${task.endDate}</td>
                    </tr>`;
    });

    htmlContent += `
                </tbody>
            </table>
        </body>
        </html>`;

    // 创建并下载文件
    const blob = new Blob([htmlContent], { type: 'application/vnd.ms-excel;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `甘特图数据-${new Date().toISOString().split('T')[0]}.xls`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

// 从JSON文件导入
export function importFromJSON(file) {
    if (!file) {
        alert('请选择JSON文件');
        return;
    }
    
    const reader = new FileReader();
    reader.onload = function(event) {
        try {
            const data = JSON.parse(event.target.result);
            
            if (!data.tasks || !Array.isArray(data.tasks)) {
                alert('无效的JSON格式：缺少tasks数组');
                return;
            }
            
            // 验证和清理数据
            const validTasks = data.tasks.filter(task => {
                return task.name && task.startDate && task.endDate;
            }).map(task => ({
                id: task.id || generateId(),
                group: task.group || '默认分组',
                number: task.number || '',
                name: task.name,
                duration: parseInt(task.duration) || 1,
                startDate: formatExcelDate(task.startDate),
                endDate: formatExcelDate(task.endDate),
                color: task.color || '#e74c3c'
            }));
            
            if (validTasks.length === 0) {
                alert('没有找到有效的任务数据');
                return;
            }
            
            // 导入数据
            if (importTasks(validTasks)) {
                // 如果有分组数据，也导入分组
                if (data.groups && Array.isArray(data.groups)) {
                    groups.length = 0;
                    groups.push(...data.groups);
                    saveGroups();
                }

                renderTasks();
                renderGanttChart();
                // 更新工具栏按钮状态
                setTimeout(() => {
                    const event = new CustomEvent('tasksUpdated');
                    document.dispatchEvent(event);
                }, 100);
                alert(`成功导入 ${validTasks.length} 个任务`);
            } else {
                alert('导入失败');
            }
            
        } catch (error) {
            alert('JSON文件解析失败：' + error.message);
        }
    };
    
    reader.readAsText(file);
}

// 从CSV文件导入
export function importFromCSV(file) {
    if (!file) {
        alert('请选择CSV文件');
        return;
    }
    
    const reader = new FileReader();
    reader.onload = function(event) {
        try {
            const csvText = event.target.result;
            const lines = csvText.split('\n').filter(line => line.trim());
            
            if (lines.length < 2) {
                alert('CSV文件格式错误：至少需要标题行和一行数据');
                return;
            }
            
            // 解析CSV行
            const parseCSVLine = (line) => {
                const result = [];
                let current = '';
                let inQuotes = false;
                
                for (let i = 0; i < line.length; i++) {
                    const char = line[i];
                    
                    if (char === '"') {
                        inQuotes = !inQuotes;
                    } else if (char === ',' && !inQuotes) {
                        result.push(current.trim());
                        current = '';
                    } else {
                        current += char;
                    }
                }
                
                result.push(current.trim());
                return result;
            };
            
            // 解析标题行
            const headers = parseCSVLine(lines[0]).map(h => h.replace(/"/g, ''));
            
            // 查找列索引
            const getColumnIndex = (columnNames) => {
                for (const name of columnNames) {
                    const index = headers.findIndex(h => h.includes(name));
                    if (index !== -1) return index;
                }
                return -1;
            };
            
            const groupIndex = getColumnIndex(['分组', '分组名', 'group']);
            const numberIndex = getColumnIndex(['编号', '工作编号', 'number']);
            const nameIndex = getColumnIndex(['名称', '工作名称', 'name', '任务名称']);
            const durationIndex = getColumnIndex(['持续', '持续时间', 'duration']);
            const startIndex = getColumnIndex(['开始', '开始时间', 'start']);
            const endIndex = getColumnIndex(['结束', '结束时间', 'end']);
            const colorIndex = getColumnIndex(['颜色', '任务颜色', 'color']);
            
            if (nameIndex === -1 || startIndex === -1 || endIndex === -1) {
                alert('CSV文件缺少必要的列：任务名称、开始时间、结束时间');
                return;
            }
            
            // 解析数据行
            const importedTasks = [];
            for (let i = 1; i < lines.length; i++) {
                const row = parseCSVLine(lines[i]).map(cell => cell.replace(/"/g, ''));
                
                if (row.length < headers.length) continue;
                
                const task = {
                    id: generateId(),
                    group: groupIndex !== -1 ? row[groupIndex] || '默认分组' : '默认分组',
                    number: numberIndex !== -1 ? row[numberIndex] || '' : '',
                    name: row[nameIndex] || '',
                    duration: durationIndex !== -1 ? parseInt(row[durationIndex]) || 1 : 1,
                    startDate: formatExcelDate(row[startIndex]),
                    endDate: formatExcelDate(row[endIndex]),
                    color: colorIndex !== -1 ? row[colorIndex] || '#e74c3c' : '#e74c3c'
                };
                
                if (task.name && task.startDate && task.endDate) {
                    importedTasks.push(task);
                }
            }
            
            if (importedTasks.length === 0) {
                alert('没有找到有效的任务数据');
                return;
            }
            
            // 导入数据
            if (importTasks(importedTasks)) {
                renderTasks();
                renderGanttChart();
                // 更新工具栏按钮状态
                setTimeout(() => {
                    const event = new CustomEvent('tasksUpdated');
                    document.dispatchEvent(event);
                }, 100);
                alert(`成功导入 ${importedTasks.length} 个任务`);
            } else {
                alert('导入失败');
            }
            
        } catch (error) {
            alert('CSV文件解析失败：' + error.message);
        }
    };
    
    reader.readAsText(file, 'utf-8');
}

// 从Excel文件导入（支持HTML格式的Excel文件）
export function importFromExcel(file) {
    if (!file) {
        alert('请选择Excel文件');
        return;
    }

    // 检查文件类型
    const fileName = file.name.toLowerCase();
    if (fileName.endsWith('.csv')) {
        importFromCSV(file);
        return;
    }

    if (!fileName.endsWith('.xlsx') && !fileName.endsWith('.xls')) {
        alert('请选择Excel文件（.xlsx或.xls）或CSV文件');
        return;
    }

    const reader = new FileReader();
    reader.onload = function(event) {
        try {
            const content = event.target.result;

            // 尝试解析HTML格式的Excel文件（我们导出的格式）
            if (content.includes('<table') && content.includes('<tr>')) {
                parseHTMLExcel(content);
            } else {
                // 对于真正的Excel文件，需要使用第三方库如SheetJS
                alert('此Excel文件格式不支持。请将Excel文件另存为CSV格式后再导入，或者使用本系统导出的Excel文件。');
            }

        } catch (error) {
            alert('Excel文件解析失败：' + error.message);
        }
    };

    reader.readAsText(file, 'utf-8');
}

// 解析HTML格式的Excel文件
function parseHTMLExcel(htmlContent) {
    try {
        // 创建临时DOM元素来解析HTML
        const parser = new DOMParser();
        const doc = parser.parseFromString(htmlContent, 'text/html');
        const table = doc.querySelector('table');

        if (!table) {
            alert('Excel文件中没有找到表格数据');
            return;
        }

        const rows = table.querySelectorAll('tr');
        if (rows.length < 2) {
            alert('Excel文件中没有足够的数据行');
            return;
        }

        // 解析表头
        const headerRow = rows[0];
        const headers = Array.from(headerRow.querySelectorAll('th, td')).map(cell => cell.textContent.trim());

        // 查找列索引
        const groupIndex = headers.findIndex(h => h.includes('分组'));
        const numberIndex = headers.findIndex(h => h.includes('编号'));
        const nameIndex = headers.findIndex(h => h.includes('工作名称') || h.includes('名称'));
        const durationIndex = headers.findIndex(h => h.includes('持续时间') || h.includes('时间'));
        const startIndex = headers.findIndex(h => h.includes('开始时间') || h.includes('开始'));
        const endIndex = headers.findIndex(h => h.includes('结束时间') || h.includes('结束'));

        if (nameIndex === -1 || startIndex === -1 || endIndex === -1) {
            alert('Excel文件格式错误：缺少必要的列（工作名称、开始时间、结束时间）');
            return;
        }

        // 解析数据行
        const importedTasks = [];
        for (let i = 1; i < rows.length; i++) {
            const row = rows[i];
            const cells = Array.from(row.querySelectorAll('td')).map(cell => cell.textContent.trim());

            if (cells.length < headers.length) continue;

            const task = {
                id: generateId(),
                group: groupIndex !== -1 ? cells[groupIndex] || '默认分组' : '默认分组',
                number: numberIndex !== -1 ? cells[numberIndex] || '' : '',
                name: cells[nameIndex] || '',
                duration: durationIndex !== -1 ? parseInt(cells[durationIndex]) || 1 : 1,
                startDate: formatExcelDate(cells[startIndex]),
                endDate: formatExcelDate(cells[endIndex]),
                color: '#e74c3c'
            };

            if (task.name && task.startDate && task.endDate) {
                importedTasks.push(task);
            }
        }

        if (importedTasks.length === 0) {
            alert('没有找到有效的任务数据');
            return;
        }

        // 导入数据
        if (importTasks(importedTasks)) {
            renderTasks();
            renderGanttChart();
            // 更新工具栏按钮状态
            setTimeout(() => {
                const event = new CustomEvent('tasksUpdated');
                document.dispatchEvent(event);
            }, 100);
            alert(`成功导入 ${importedTasks.length} 个任务`);
        } else {
            alert('导入失败');
        }

    } catch (error) {
        alert('Excel文件解析失败：' + error.message);
    }
}

// 通用导入函数
export function importData(file) {
    if (!file) return;
    
    const fileName = file.name.toLowerCase();
    
    if (fileName.endsWith('.json')) {
        importFromJSON(file);
    } else if (fileName.endsWith('.csv')) {
        importFromCSV(file);
    } else if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls')) {
        importFromExcel(file);
    } else {
        alert('不支持的文件格式。请选择JSON、CSV或Excel文件');
    }
}

// 生成示例数据
export function generateSampleData() {
    const sampleTasks = [
        {
            id: generateId(),
            group: '前期',
            number: '001',
            name: '需求分析',
            duration: 10,
            startDate: '2025-08-09',
            endDate: '2025-08-18',
            color: '#3498db'
        },
        {
            id: generateId(),
            group: '前期',
            number: '002',
            name: '设计方案',
            duration: 15,
            startDate: '2025-08-19',
            endDate: '2025-09-02',
            color: '#2ecc71'
        },
        {
            id: generateId(),
            group: '幕墙施工',
            number: '003',
            name: '材料采购',
            duration: 7,
            startDate: '2025-09-03',
            endDate: '2025-09-09',
            color: '#e74c3c'
        },
        {
            id: generateId(),
            group: '幕墙施工',
            number: '004',
            name: '现场施工',
            duration: 30,
            startDate: '2025-09-10',
            endDate: '2025-10-09',
            color: '#f39c12'
        },
        {
            id: generateId(),
            group: '后期',
            number: '005',
            name: '质量检测',
            duration: 5,
            startDate: '2025-10-10',
            endDate: '2025-10-14',
            color: '#9b59b6'
        },
        {
            id: generateId(),
            group: '后期',
            number: '006',
            name: '项目验收',
            duration: 3,
            startDate: '2025-10-15',
            endDate: '2025-10-17',
            color: '#1abc9c'
        }
    ];
    
    return sampleTasks;
}

// 添加示例数据
export function addSampleData() {
    if (tasks.length === 0) {
        const sampleTasks = generateSampleData();
        if (importTasks(sampleTasks)) {
            renderTasks();
            renderGanttChart();
            alert('已添加示例数据');
        }
    } else {
        if (confirm('当前已有数据，是否要替换为示例数据？')) {
            const sampleTasks = generateSampleData();
            if (importTasks(sampleTasks)) {
                renderTasks();
                renderGanttChart();
                alert('已替换为示例数据');
            }
        }
    }
}

// 导出模板文件
export function exportTemplate() {
    const templateData = {
        tasks: [
            {
                group: '示例分组',
                number: '001',
                name: '示例任务',
                duration: 5,
                startDate: '2025-08-15',
                endDate: '2025-08-19',
                color: '#3498db'
            }
        ],
        groups: ['示例分组'],
        exportDate: new Date().toISOString(),
        version: '1.0',
        description: '这是一个甘特图数据模板文件'
    };
    
    const blob = new Blob([JSON.stringify(templateData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'gantt-template.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

// 导出CSV模板
export function exportCSVTemplate() {
    const headers = ['序号', '分组名', '工作编号', '工作名称', '持续时间', '开始时间', '结束时间', '任务颜色'];
    const sampleRow = ['1', '示例分组', '001', '示例任务', '5', '2025-08-15', '2025-08-19', '#3498db'];
    
    const csvContent = [headers, sampleRow]
        .map(row => row.map(field => `"${field}"`).join(','))
        .join('\n');
    
    const bom = '\uFEFF';
    const blob = new Blob([bom + csvContent], { type: 'text/csv;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'gantt-template.csv';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}
