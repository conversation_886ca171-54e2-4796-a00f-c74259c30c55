<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务条拖拽功能使用指南</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        .guide-container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            max-width: 1200px;
            margin: 0 auto 20px auto;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        h2 {
            color: #34495e;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-top: 40px;
        }
        h3 {
            color: #2980b9;
            margin-top: 25px;
            font-size: 1.3em;
        }
        .feature-box {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .demo-area {
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            text-align: center;
        }
        .task-bar-demo {
            background: #3498db;
            color: white;
            padding: 8px 15px;
            border-radius: 4px;
            display: inline-block;
            position: relative;
            margin: 10px;
            cursor: move;
            user-select: none;
        }
        .handle-demo {
            position: absolute;
            top: 0;
            width: 6px;
            height: 100%;
            background: rgba(255,255,255,0.8);
            cursor: ew-resize;
        }
        .handle-demo.left {
            left: 0;
            border-radius: 4px 0 0 4px;
        }
        .handle-demo.right {
            right: 0;
            border-radius: 0 4px 4px 0;
        }
        .instruction-list {
            background: #e8f4fd;
            border-left: 4px solid #3498db;
            padding: 20px;
            margin: 15px 0;
        }
        .instruction-list ol {
            margin: 0;
            padding-left: 20px;
        }
        .instruction-list li {
            margin: 8px 0;
        }
        .tip-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .success-box {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .comparison-table th,
        .comparison-table td {
            border: 1px solid #dee2e6;
            padding: 12px;
            text-align: left;
        }
        .comparison-table th {
            background: #f8f9fa;
            font-weight: bold;
        }
        .comparison-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        code {
            background: #f1f3f4;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            color: #d73a49;
        }
        .cursor-demo {
            display: inline-block;
            padding: 5px 10px;
            margin: 5px;
            border: 2px solid #ddd;
            border-radius: 5px;
            background: #fff;
        }
        .cursor-move { cursor: move; }
        .cursor-resize { cursor: ew-resize; }
        .cursor-pointer { cursor: pointer; }
    </style>
</head>
<body>
    <div class="guide-container">
        <h1>🎯 任务条拖拽功能使用指南</h1>
        
        <div class="feature-box">
            <h2 style="color: white; border: none; margin-top: 0;">✨ 功能概述</h2>
            <p>横道图应用现在支持三种任务条拖拽操作：</p>
            <ul>
                <li><strong>🔄 整体移动</strong>：拖拽任务条中间区域，保持持续时间不变</li>
                <li><strong>📏 左端调整</strong>：拖拽左侧手柄，调整开始时间</li>
                <li><strong>📐 右端调整</strong>：拖拽右侧手柄，调整结束时间</li>
            </ul>
        </div>

        <h2>🎮 操作方式</h2>
        
        <h3>1. 整体移动功能</h3>
        <div class="instruction-list">
            <ol>
                <li><strong>定位中间区域</strong>：将鼠标悬停在任务条的中间区域（排除左右6px的手柄区域）</li>
                <li><strong>确认光标</strong>：鼠标光标变为 <span class="cursor-demo cursor-move">移动光标 ↔</span></li>
                <li><strong>开始拖拽</strong>：按住鼠标左键并拖拽</li>
                <li><strong>实时预览</strong>：拖拽过程中任务条会实时移动</li>
                <li><strong>释放完成</strong>：释放鼠标完成移动，数据自动更新</li>
            </ol>
        </div>

        <div class="demo-area">
            <div class="task-bar-demo">
                <div class="handle-demo left"></div>
                <div class="handle-demo right"></div>
                示例任务条 - 拖拽中间区域进行整体移动
            </div>
            <p><em>↑ 演示：中间区域用于整体移动，左右两端为调整手柄</em></p>
        </div>

        <h3>2. 左端调整功能</h3>
        <div class="instruction-list">
            <ol>
                <li><strong>定位左侧手柄</strong>：将鼠标悬停在任务条的最左侧6px区域</li>
                <li><strong>确认光标</strong>：鼠标光标变为 <span class="cursor-demo cursor-resize">调整光标 ↔</span></li>
                <li><strong>拖拽调整</strong>：向左拖拽提前开始时间，向右拖拽延后开始时间</li>
                <li><strong>结束时间保持</strong>：结束时间不变，持续时间自动重新计算</li>
            </ol>
        </div>

        <h3>3. 右端调整功能</h3>
        <div class="instruction-list">
            <ol>
                <li><strong>定位右侧手柄</strong>：将鼠标悬停在任务条的最右侧6px区域</li>
                <li><strong>确认光标</strong>：鼠标光标变为 <span class="cursor-demo cursor-resize">调整光标 ↔</span></li>
                <li><strong>拖拽调整</strong>：向右拖拽延长任务，向左拖拽缩短任务</li>
                <li><strong>开始时间保持</strong>：开始时间不变，持续时间自动重新计算</li>
            </ol>
        </div>

        <h2>📊 功能对比</h2>
        
        <table class="comparison-table">
            <thead>
                <tr>
                    <th>操作类型</th>
                    <th>拖拽区域</th>
                    <th>鼠标光标</th>
                    <th>开始时间</th>
                    <th>结束时间</th>
                    <th>持续时间</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>整体移动</strong></td>
                    <td>任务条中间区域</td>
                    <td>move (↔)</td>
                    <td>同步变化</td>
                    <td>同步变化</td>
                    <td>保持不变</td>
                </tr>
                <tr>
                    <td><strong>左端调整</strong></td>
                    <td>左侧6px手柄</td>
                    <td>ew-resize (↔)</td>
                    <td>变化</td>
                    <td>保持不变</td>
                    <td>重新计算</td>
                </tr>
                <tr>
                    <td><strong>右端调整</strong></td>
                    <td>右侧6px手柄</td>
                    <td>ew-resize (↔)</td>
                    <td>保持不变</td>
                    <td>变化</td>
                    <td>重新计算</td>
                </tr>
            </tbody>
        </table>

        <h2>🧪 实际测试结果</h2>
        
        <div class="success-box">
            <h3>✅ 整体移动功能测试</h3>
            <ul>
                <li><strong>项目启动任务</strong>：从 2025-08-09~2025-08-13 移动到 2025-08-15~2025-08-19（保持5天）</li>
                <li><strong>需求分析任务</strong>：从 2025-08-14~2025-08-28 移动到 2025-08-10~2025-08-24（保持15天）</li>
                <li><strong>材料采购任务</strong>：在7天步长下从 2025-08-13~2025-08-19 移动到 2025-09-10~2025-09-16（保持7天）</li>
            </ul>
        </div>

        <div class="success-box">
            <h3>✅ 调整手柄功能测试</h3>
            <ul>
                <li><strong>右侧手柄</strong>：项目启动任务从5天延长到8天（2025-08-15~2025-08-22）</li>
                <li><strong>功能兼容</strong>：整体移动和调整手柄功能完美兼容，无冲突</li>
            </ul>
        </div>

        <div class="success-box">
            <h3>✅ 步长兼容性测试</h3>
            <ul>
                <li><strong>1天/格步长</strong>：拖拽精度为1天</li>
                <li><strong>7天/格步长</strong>：拖拽精度为7天的倍数</li>
                <li><strong>步长切换</strong>：修改步长后拖拽功能正常工作</li>
            </ul>
        </div>

        <h2>💡 使用技巧</h2>
        
        <div class="tip-box">
            <h3>🎯 精确操作技巧</h3>
            <ul>
                <li><strong>光标识别</strong>：根据鼠标光标判断当前操作模式</li>
                <li><strong>步长设置</strong>：调整步长可以控制拖拽的精度</li>
                <li><strong>视觉反馈</strong>：拖拽时任务条会有高亮和阴影效果</li>
                <li><strong>数据同步</strong>：拖拽完成后表格数据自动更新</li>
            </ul>
        </div>

        <div class="tip-box">
            <h3>⚠️ 注意事项</h3>
            <ul>
                <li><strong>最小持续时间</strong>：任务持续时间至少为1天</li>
                <li><strong>操作互斥</strong>：同时只能进行一种拖拽操作</li>
                <li><strong>数据保存</strong>：所有修改会自动保存到本地存储</li>
                <li><strong>页面刷新</strong>：刷新页面后拖拽修改的数据会保持</li>
            </ul>
        </div>

        <h2>🔧 技术实现</h2>
        
        <div class="instruction-list">
            <h3>核心功能</h3>
            <ul>
                <li><strong>DOM结构</strong>：每个任务条包含左手柄、右手柄、移动区域三个子元素</li>
                <li><strong>事件处理</strong>：mousedown、mousemove、mouseup事件的完整处理链</li>
                <li><strong>状态管理</strong>：共享状态对象防止操作冲突</li>
                <li><strong>数据同步</strong>：实时更新任务数据和表格显示</li>
                <li><strong>视觉反馈</strong>：CSS动画和样式变化提供用户反馈</li>
            </ul>
        </div>

        <div class="instruction-list">
            <h3>兼容性保证</h3>
            <ul>
                <li><strong>步长兼容</strong>：支持任意步长设置，移动精度自动适配</li>
                <li><strong>功能兼容</strong>：与现有的任务移动、复制、选择功能无冲突</li>
                <li><strong>样式兼容</strong>：保持与现有UI风格的一致性</li>
                <li><strong>数据兼容</strong>：与现有的数据结构和存储机制兼容</li>
            </ul>
        </div>

        <div style="text-align: center; margin-top: 40px;">
            <button onclick="window.open('index.html', '_blank')" 
                    style="background: #3498db; color: white; border: none; padding: 15px 30px; 
                           border-radius: 8px; font-size: 16px; cursor: pointer; 
                           box-shadow: 0 2px 10px rgba(52,152,219,0.3);">
                🚀 打开横道图应用体验拖拽功能
            </button>
        </div>
    </div>
</body>
</html>
