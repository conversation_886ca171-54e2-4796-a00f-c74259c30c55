/* 全局样式 */
:root {
    --row-height: 45px; /* 固定行高 */
    --height-margin: 10px; /* 行高与任务条高度的固定差值 */
    --task-bar-height: calc(var(--row-height) - var(--height-margin)); /* 任务条高度 = 行高 - 固定差值 */
    --unit-width: 40px;
    --primary-font: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    --font-size-small: 11px;
    --font-size-normal: 12px;
    --font-size-medium: 14px;
    --font-size-large: 16px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--primary-font);
    font-size: var(--font-size-normal);
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.4;
    padding: 20px;
}

.container {
    max-width: 100%;
    margin: 0 auto;
    padding: 0;
}

header {
    text-align: center;
    margin-bottom: 0;
}

h1 {
    color: #2c3e50;
    font-size: 2em;
}

/* 工具栏样式 */
.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #fff;
    padding: 15px;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.toolbar .ruler-control {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
}

.toolbar .ruler-control label {
    font-family: var(--primary-font);
    font-size: var(--font-size-normal);
    color: #333;
    font-weight: 500;
}

.toolbar .ruler-control select,
.toolbar .ruler-control input {
    font-family: var(--primary-font);
    font-size: var(--font-size-normal);
    padding: 4px 6px;
    min-width: 60px;
}

.toolbar .date-range-display {
    font-family: var(--primary-font);
    font-size: var(--font-size-normal);
    font-weight: 500;
}

.toolbar .date-range-display span {
    font-size: var(--font-size-normal);
}

.move-days-container {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    margin: 0 10px;
    white-space: nowrap;
}

.move-days-container label {
    font-family: var(--primary-font);
    font-size: var(--font-size-normal);
    color: #333;
    font-weight: 500;
}

.move-days-container input {
    font-family: var(--primary-font);
    width: 50px;
    padding: 3px 6px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: var(--font-size-normal);
    text-align: center;
}

/* 按钮样式 */
button {
    font-family: var(--primary-font);
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: var(--font-size-normal);
    font-weight: 500;
    transition: all 0.3s ease;
    letter-spacing: 0.3px;
}

.btn-primary {
    background-color: #3498db;
    color: white;
}

.btn-primary:hover {
    background-color: #2980b9;
}

.btn-secondary {
    background-color: #95a5a6;
    color: white;
}

.btn-secondary:hover {
    background-color: #7f8c8d;
}

.btn-danger {
    background-color: #e74c3c;
    color: white;
}

.btn-danger:hover {
    background-color: #c0392b;
}

button:disabled {
    background-color: #bdc3c7;
    cursor: not-allowed;
}

/* 统一滚动区域样式 */
.main-content {
    /* 移除固定高度约束，让内容决定高度 */
    margin: 0;
    padding: 0;
    overflow: hidden;
}

.unified-panel {
    display: flex;
    flex-direction: column;
    /* 移除固定高度约束，让内容决定高度 */
    width: 100%;
}

.unified-header {
    display: flex;
    background-color: #ecf0f1;
    border-bottom: 1px solid #ddd;
    flex-shrink: 0;
    height: calc(var(--row-height) * 2); /* 表头总高度：行高 × 2行 */
    position: sticky;
    top: 0;
    z-index: 2; /* 降低z-index，避免遮挡任务条 */
}

.task-header {
    width: 770px; /* 优化后的总宽度 */
    flex-shrink: 0;
    border-right: 1px solid #ddd;
    position: relative; /* 用于放置任务栏总宽度拖拽手柄 */
}

/* 左侧任务栏紧凑化 */
.task-header th,
#taskTableBody td {
    padding: 0 6px; /* 减少内边距 */
}

/* 左侧任务栏 sticky 固定（前7列） */
.freeze-col { position: sticky; left: 0; }
.unified-header thead th.freeze-col { z-index: 3; background: #ecf0f1; }
#taskTableBody td.freeze-col { z-index: 2; background: #fff; }
/* 为每一列叠加偏移，确保列彼此不覆盖。偏移将由JS在初始化时计算并设置 inline-style。*/

/* 列宽调整手柄（表头） */
.resize-handle-col {
    position: absolute;
    top: 0;
    right: 0;
    width: 6px;
    height: 100%;
    cursor: col-resize;
    background: transparent;
}
.resize-handle-col:hover { background: rgba(52,152,219,0.25); }

/* 任务栏总宽度调整手柄（在任务栏右边界） */
.tasklist-resize-handle {
    position: absolute;
    top: 0;
    right: -3px;
    width: 6px;
    height: 100%;
    cursor: col-resize;
    background: rgba(0,0,0,0);
    z-index: 4;
}
.tasklist-resize-handle:hover { background: rgba(46,204,113,0.25); }

.gantt-header {
    flex: 1;
    overflow: hidden;
}

.unified-body {
    /* 移除flex: 1，让内容决定高度 */
    overflow-y: auto; /* 统一垂直滚动 */
    overflow-x: auto; /* 允许水平滚动 */
    max-height: calc(100vh - 200px); /* 设置最大高度以防止页面过长 */
}

.unified-content {
    display: flex;
    /* 移除min-height约束，让表格内容决定高度 */
    overflow: auto;
}

/* 统一表格样式 - 新的解决方案 */
#unifiedTaskTable {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed;
}

/* 统一表格标题行样式 - 使用更具体的选择器和!important */
table thead th,
.task-header th {
    font-size: 12px !important; /* 表格标题行字体大小改为12px */
    font-weight: bold !important; /* 表格标题行加粗 */
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
    background-color: #ecf0f1;
    border: 1px solid #ddd;
    padding: 2px 4px;
    text-align: left;
    vertical-align: middle;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 统一表头居中对齐（覆盖默认左对齐） */
.unified-header thead th {
    text-align: center !important;
}

/* 甘特图表头：通用基础 */
.unified-header thead th {
    box-sizing: border-box;
    vertical-align: middle;
}

/* 甘特图表头：月份行 */
.unified-header thead #monthRow th.month-header {
    height: var(--row-height);
    line-height: var(--row-height);
    background: #f8f9fa;
    color: #333;
    font-size: 12px;
    font-weight: 700;
    padding: 0 4px;
    white-space: nowrap;
    border-top: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
    border-right: 0; /* 避免与日期格右边框叠加形成双线 */
}

/* 甘特图表头：日期行 */
.unified-header thead #dateRow th.time-unit {
    min-width: var(--unit-width);
    width: var(--unit-width);
    height: var(--row-height);
    line-height: var(--row-height);
    background: #fff;
    color: #333;
    font-size: 12px;
    font-weight: 600;
    text-align: center;
    padding: 0 2px;
    white-space: nowrap;
    border-right: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
}
.unified-header thead #dateRow th.time-unit:last-child {
    border-right: none;
}

/* 覆盖历史 time-unit 样式中的高度与换行，保持一致性 */
.time-unit {
    height: var(--row-height) !important;
    line-height: var(--row-height) !important;
    white-space: nowrap !important;
}



/* 日期单元格样式 - 强制12px加粗 */
.date-unit {
    font-size: 12px !important; /* 日期单元格字体大小改为12px */
    font-weight: bold !important; /* 日期单元格加粗 */
}

/* 时间标尺标题样式 - 使用更具体的选择器 */
table thead tr:nth-child(1) th:nth-child(8),
table thead tr:nth-child(1) th:last-child {
    font-size: 12px !important; /* 时间标尺标题字体大小改为12px */
    font-weight: bold !important; /* 时间标尺标题加粗 */
}

#unifiedTaskTable tbody tr {
    height: var(--row-height); /* 使用统一的行高 */
    border-bottom: 1px solid #eee;
}

#unifiedTaskTable tbody tr:hover {
    background-color: #f9f9f9;
}

#unifiedTaskTable tbody tr.selected {
    background-color: #e3f2fd;
}

/* 任务信息列样式 - 紧凑设计 */
.task-info-cell {
    padding: 2px 4px; /* 进一步减少内边距 */
    border-right: 1px solid #ddd;
    vertical-align: middle;
    white-space: nowrap; /* 不换行，保持紧凑 */
    overflow: hidden;
    text-overflow: ellipsis;
    height: var(--row-height); /* 使用统一的行高 */
    line-height: var(--row-height); /* 垂直居中 */
    font-size: 11px; /* 更小的字体以适应紧凑布局 */
}

/* 甘特图列样式 */
.gantt-cell {
    position: relative;
    padding: 0;
    border-right: none;
    height: var(--row-height); /* 使用统一的行高 */
    min-width: 800px; /* 确保甘特图区域有足够宽度 */
    background-image: repeating-linear-gradient(
        90deg,
        transparent,
        transparent 39px,
        #ddd 39px,
        #ddd 40px
    );
}

/* 甘特图任务条在表格单元格内的样式 */
.gantt-task-in-cell {
    position: absolute;
    height: var(--task-bar-height); /* 使用统一的任务条高度 */
    top: calc((var(--row-height) - var(--task-bar-height)) / 2); /* 在单元格中垂直居中 */
    border-radius: 2px;
    display: flex;
    align-items: center;
    padding: 0 4px; /* 进一步减少内边距 */
    color: black; /* 甘特图任务条内文本颜色改为黑色 */
    font-size: 12px; /* 甘特图任务条内文本字体大小改为12px */
    font-weight: bold; /* 甘特图任务条内文本加粗 */
    box-shadow: 0 1px 2px rgba(0,0,0,0.2);
    cursor: pointer;
    transition: all 0.2s ease;
    z-index: 10; /* 提高z-index确保在时间标尺之上 */
}

.gantt-task-in-cell:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0,0,0,0.3);
    z-index: 11; /* 悬停时进一步提升层级 */
}

/* 任务条调整手柄样式 */
.task-resize-handle {
    position: absolute;
    top: 0;
    width: 6px;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.3);
    opacity: 0;
    transition: opacity 0.2s ease;
    z-index: 12;
}

.task-resize-handle:hover,
.gantt-task-in-cell:hover .task-resize-handle {
    opacity: 1;
    background-color: rgba(255, 255, 255, 0.8);
}

.task-resize-handle.left-handle {
    left: 0;
    border-radius: 2px 0 0 2px;
}

.task-resize-handle.right-handle {
    right: 0;
    border-radius: 0 2px 2px 0;
}

/* 拖拽调整时的视觉反馈 */
.gantt-task-in-cell.resizing {
    box-shadow: 0 3px 10px rgba(0,0,0,0.4);
    z-index: 15;
    opacity: 0.9;
}

.gantt-task-in-cell.resizing .task-resize-handle {
    opacity: 1;
    background-color: rgba(255, 255, 255, 1);
}

/* 任务条移动区域样式 */
.task-move-area {
    background-color: transparent;
    transition: background-color 0.2s ease;
}

.task-move-area:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* 任务条移动时的视觉反馈 */
.gantt-task-in-cell.moving {
    box-shadow: 0 4px 12px rgba(0,0,0,0.5);
    z-index: 20;
    opacity: 0.8;
    transform: scale(1.02);
    transition: transform 0.1s ease;
}

.gantt-task-in-cell.moving .task-resize-handle {
    opacity: 0.5;
}

.gantt-task-in-cell.moving .task-move-area {
    background-color: rgba(255, 255, 255, 0.2);
}

/* 保留原有样式作为备用 */
.task-list {
    width: 770px; /* 优化后的总宽度 */
    flex-shrink: 0;
    border-right: 1px solid #ddd;
}

.gantt-chart {
    flex: 1;
    position: relative;
    min-height: 100%;
    overflow: visible; /* 防止出现独立滚动条 */
}

/* 任务列表样式 */
.task-list {
    background-color: #fff;
    border-right: 1px solid #ddd;
    height: 100%;
}

.task-list table {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed;
    border: 1px solid #ddd;
}

.task-list th,
.task-list td {
    font-family: var(--primary-font);
    border: 1px solid #ddd;
    border-collapse: collapse;
    padding: 0 6px;
    text-align: left;
    height: var(--row-height);
    line-height: var(--row-height);
    box-sizing: border-box;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    position: relative;
    transition: height 0.3s ease;
    font-size: var(--font-size-small);
}

/* 列宽调整手柄 */
.resize-handle {
    position: absolute;
    top: 0;
    right: 0;
    width: 5px;
    height: 100%;
    background-color: transparent;
    cursor: col-resize;
    z-index: 10;
}

.resize-handle:hover {
    background-color: rgba(52, 152, 219, 0.3);
}

/* 任务表格样式 */
.task-header table {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed;
}

.task-header th {
    font-family: var(--primary-font);
    padding: 0 15px;
    text-align: left;
    border-bottom: 1px solid #ddd;
    height: var(--row-height);
    line-height: var(--row-height);
    box-sizing: border-box;
    background-color: #ecf0f1;
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 12px !important; /* 表格标题行字体大小改为12px */
    transition: height 0.3s ease;
}

.task-panel th,
.task-panel td {
    padding: 0 15px;
    text-align: left;
    border-bottom: 1px solid #ddd;
    height: var(--row-height);
    line-height: var(--row-height);
    box-sizing: border-box;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    position: relative;
    transition: height 0.3s ease;
}

.task-panel th {
    background-color: #ecf0f1;
    font-weight: bold;
}

/* 任务表格行 - 跨浏览器精确对齐 */
.task-panel tbody tr {
    height: var(--row-height);
    line-height: var(--row-height);
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    position: relative;
    display: table-row;
    /* 强制使用整数像素值，避免浏览器舍入差异 */
    transform: translateZ(0);
}

.task-panel tbody tr:hover {
    background-color: #f5f5f5;
}

.task-panel tbody tr.selected {
    background-color: #d6eaf8;
}

.task-panel tbody tr.dragging {
    opacity: 0.5;
    background-color: #ffeb3b;
}

.task-panel tbody tr.drag-over {
    background-color: #c8e6c9;
    border-top: 2px solid #4caf50;
}

/* 优化文本显示 - 跨浏览器精确对齐 */
/* 表格标题行样式 */
.task-header th {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 12px; /* 表格标题行字体大小改为12px */
    font-weight: bold; /* 表格标题行加粗 */
    padding: 2px 4px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    height: var(--row-height); /* 使用统一的行高 */
    line-height: var(--row-height); /* 使用统一的行高 */
    transition: height 0.3s ease;
    margin: 0;
    box-sizing: border-box;
    vertical-align: middle;
    /* 强制使用整数像素值，避免浏览器舍入差异 */
    transform: translateZ(0);
}

/* 表格数据行样式 */
#taskTableBody td {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 12px; /* 表格数据行字体大小改为12px */
    font-weight: bold; /* 表格数据行加粗 */
    padding: 2px 4px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    height: var(--row-height); /* 使用统一的行高 */
    line-height: var(--row-height); /* 使用统一的行高 */
    transition: height 0.3s ease;
    margin: 0;
    box-sizing: border-box;
    vertical-align: middle;
    /* 强制使用整数像素值，避免浏览器舍入差异 */
    transform: translateZ(0);
}

/* 工作名称列特殊处理 - 移除特殊字体设置，使用统一的12px加粗 */
/* #taskTableBody td:nth-child(4) {
    font-size: 13px;
    font-weight: 500;
} */

/* 鼠标悬停时显示完整文本 */
#taskTableBody td {
    position: relative;
}

#taskTableBody td:hover {
    overflow: visible;
    z-index: 100;
}

#taskTableBody td:hover::after {
    content: attr(data-content);
    position: absolute;
    top: 100%;
    left: 0;
    background: #2c3e50;
    color: white;
    padding: 6px 10px;
    border-radius: 4px;
    white-space: nowrap;
    z-index: 1000;
    font-size: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    max-width: 300px;
    overflow: visible;
}

/* 统一列宽设置 */
/* 紧凑列宽设置 - 减少左右间距 */
.task-header th:nth-child(1),
.task-list td:nth-child(1) {
    width: 35px;
    min-width: 35px;
    max-width: 35px;
    text-align: center;
}

.task-header th:nth-child(2),
.task-list td:nth-child(2) {
    width: 100px;
    min-width: 100px;
    max-width: 100px;
}

.task-header th:nth-child(3),
.task-list td:nth-child(3) {
    width: 60px;
    min-width: 60px;
    max-width: 60px;
    text-align: center;
}

.task-header th:nth-child(4),
.task-list td:nth-child(4) {
    width: 200px;
    min-width: 200px;
    max-width: 200px;
}

.task-header th:nth-child(5),
.task-list td:nth-child(5) {
    width: 75px;
    min-width: 75px;
    max-width: 75px;
    text-align: center;
}

.task-header th:nth-child(6),
.task-list td:nth-child(6) {
    width: 95px;
    min-width: 95px;
    max-width: 95px;
}

.task-header th:nth-child(7),
.task-list td:nth-child(7) {
    width: 95px;
    min-width: 95px;
    max-width: 95px;
}

/* 甘特图容器 */
.gantt-chart {
    position: relative;
    /* 移除固定高度，让内容决定高度 */
    background-image: repeating-linear-gradient(
        90deg,
        transparent,
        transparent calc(var(--unit-width) - 1px),
        #ddd calc(var(--unit-width) - 1px),
        #ddd var(--unit-width)
    );
}

#ganttChart {
    position: relative;
    /* 移除min-height约束，让内容决定高度 */
    width: 100%;
}

/* 优化滚动条样式 */
.unified-body::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.unified-body::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.unified-body::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

.unified-body::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* 时间标尺 */
#timeRuler {
    display: flex;
    flex-direction: column;
    height: 120px;
    background-color: #ecf0f1;
    z-index: 100;
    position: relative;
    width: 100%; /* 确保宽度适应 */
    pointer-events: none; /* 防止遮挡甘特图条的点击 */
}

.month-row {
    display: flex;
    height: var(--row-height); /* 使用统一的行高 */
    border-bottom: 1px solid #ddd;
    z-index: 1; /* 降低z-index，不遮挡任务条 */
    pointer-events: none; /* 防止遮挡任务条的点击 */
    background-color: #f8f9fa; /* 月份行背景色 */
}

.month-unit {
    padding: 0 5px;
    text-align: center;
    border-right: 1px solid #ddd;
    font-size: 11px; /* 缩小字体 */
    font-weight: bold;
    height: var(--row-height); /* 使用统一的行高 */
    line-height: var(--row-height); /* 使用统一的行高 */
    box-sizing: border-box;
    background-color: #f8f9fa;
}

.date-row {
    display: flex;
    height: var(--row-height); /* 使用统一的行高 */
    background-color: #ffffff; /* 日期行背景色 */
    position: relative;
    z-index: 1; /* 降低z-index，不遮挡任务条 */
    pointer-events: none; /* 防止遮挡任务条的点击 */
}

.date-unit {
    min-width: var(--unit-width);
    width: var(--unit-width);
    padding: 0;
    text-align: center;
    border-right: 1px solid #ddd;
    font-size: 12px !important; /* 时间标尺字体大小改为12px */
    height: var(--row-height); /* 使用统一的行高 */
    line-height: var(--row-height); /* 使用统一的行高 */
    box-sizing: border-box;
    color: #000;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    border: 1px solid #ddd;
    pointer-events: none; /* 防止遮挡甘特图条的点击 */
}

/* 甘特图行样式 */
.gantt-row {
    position: relative; /* 为绝对定位的任务条提供定位上下文 */
    width: 100%;
    height: var(--row-height);
    margin: 0;
    padding: 0;
    border: none;
    background: transparent;
    display: flex;
    align-items: stretch; /* 让子元素填满高度 */
}

/* 甘特图任务条容器 - 跨浏览器精确对齐 */
.gantt-task-container {
    position: absolute;
    width: 100%;
    height: var(--row-height);
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    left: 0;
    /* 强制使用整数像素值，避免浏览器舍入差异 */
    transform: translateZ(0);
    display: flex;
    align-items: center; /* 垂直居中对齐 */
}

.gantt-task {
    font-family: var(--primary-font);
    position: absolute; /* 改为绝对定位，确保任务条正确显示在指定位置 */
    height: var(--task-bar-height);
    border-radius: 4px;
    color: black; /* 甘特图任务条内文本颜色改为黑色 */
    font-size: 12px; /* 甘特图任务条内文本字体大小改为12px */
    font-weight: bold; /* 甘特图任务条内文本加粗 */
    overflow: hidden;
    cursor: grab;
    margin: 0;
    line-height: var(--task-bar-height);
    padding: 0 8px;
    border: 1px solid rgba(0,0,0,0.2); /* 调整边框颜色以适应黑色字体 */
    box-sizing: border-box;
    transition: all 0.3s ease;
    /* 强制使用整数像素值，避免浏览器舍入差异 */
    transform: translateZ(0);
    display: flex;
    align-items: center;
    white-space: nowrap;
    text-overflow: ellipsis;
    user-select: none; /* 防止拖拽时选中文本 */
    text-shadow: 1px 1px 1px rgba(255,255,255,0.8); /* 添加白色文字阴影增强可读性 */
    letter-spacing: 0.3px;
    top: 1px; /* 添加垂直偏移，确保在行内正确居中 */
    z-index: 10; /* 确保任务条显示在其他元素之上 */
}

/* 拖拽状态样式 */
.gantt-task:active {
    cursor: grabbing;
}

.gantt-task.dragging {
    opacity: 0.7;
    z-index: 1000;
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    transform: scale(1.05);
}

/* 拖拽目标位置指示器 */
.drop-indicator {
    position: absolute;
    width: 100%;
    height: 2px;
    background-color: #3498db;
    z-index: 999;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.drop-indicator.active {
    opacity: 1;
}

/* 时间轴拖拽指示器 */
.time-drop-indicator {
    position: absolute;
    width: 2px;
    height: 100%;
    background-color: #e74c3c;
    z-index: 999;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.time-drop-indicator.active {
    opacity: 1;
}

.gantt-task-label {
    position: absolute;
    height: var(--task-height, 50px);
    top: 0;
    color: #333;
    font-size: 16px;
    font-weight: bold;
    line-height: var(--task-height, 50px);
    padding: 0 5px;
    white-space: nowrap;
    transition: all 0.3s ease;
    margin: 0;
    box-sizing: border-box;
    /* 强制使用整数像素值，避免浏览器舍入差异 */
    transform: translateZ(0);
    vertical-align: middle;
}

.gantt-task-resize-handle {
    position: absolute;
    right: 0;
    top: 0;
    width: 10px;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.2);
    cursor: ew-resize;
}

.time-unit {
    min-width: var(--unit-width);
    padding: 0 5px;
    text-align: center;
    border-right: 1px solid #ddd;
    font-size: 12px;
    height: 50px;
    line-height: 25px;
    box-sizing: border-box;
    white-space: pre-line;
}



.gantt-task:active {
    cursor: grabbing;
}

.gantt-task:hover {
    opacity: 0.8;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: #fff;
    margin: 10% auto;
    padding: 20px;
    border-radius: 5px;
    width: 80%;
    max-width: 500px;
    position: relative;
}

.close {
    position: absolute;
    right: 15px;
    top: 15px;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: #e74c3c;
}

/* 表单样式 */
.form-group {
    margin-bottom: 15px;
}

label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

input, select {
    font-family: var(--primary-font);
    width: 100%;
    padding: 6px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: var(--font-size-normal);
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

/* 分组列表 */
.group-list ul {
    list-style-type: none;
}

.group-list li {
    display: flex;
    justify-content: space-between;
    padding: 10px;
    border-bottom: 1px solid #ddd;
}

.group-list li:last-child {
    border-bottom: none;
}

.delete-group {
    color: #e74c3c;
    cursor: pointer;
}

.delete-group:hover {
    text-decoration: underline;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-content {
        flex-direction: column;
        height: auto;
    }

    .task-panel, .gantt-panel {
        max-height: 500px;
    }

    .toolbar {
        flex-direction: column;
        gap: 10px;
        align-items: flex-start;
    }
}

/* 勾选框样式优化 */
.task-checkbox {
    margin: 0;
    width: 14px;
    height: 14px;
    cursor: pointer;
    transform: scale(1.0); /* 减小缩放比例，使复选框与文字大小更协调 */
    accent-color: #3498db;
    vertical-align: middle;
}

input[type="checkbox"]#selectAll {
    width: 14px;
    height: 14px;
    cursor: pointer;
    accent-color: #3498db;
    transform: scale(1.0);
}

input[type="checkbox"]:hover {
    transform: scale(1.4);
    transition: transform 0.2s ease;
}

/* 确保勾选框点击区域足够大 */
th:first-child, td:first-child {
    text-align: center;
    padding: 0 10px;
    min-width: 50px;
}

/* 垂直拖拽提示样式 */
.vertical-drag-hint {
    position: fixed;
    top: 10px;
    right: 10px;
    background-color: rgba(52, 152, 219, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 2000;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.vertical-drag-hint.show {
    opacity: 1;
}