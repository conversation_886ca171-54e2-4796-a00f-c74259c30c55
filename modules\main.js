// 主入口文件
// 负责应用的初始化和各模块的协调

import { tasks, addSampleData } from './data-manager.js';
import { 
    initDOMElements as initUIElements, 
    renderTasks, 
    renderGanttChart, 
    updateDateRangeDisplay,
    adjustGanttBodyHeight,
    fixScrollAlignment
} from './ui-renderer.js';
import { 
    initDOMElements as initEventElements, 
    bindEventListeners, 
    updateToolbarButtons 
} from './event-handler.js';
import { initDragHandlers } from './drag-handler.js';

// 应用状态
let isInitialized = false;

// 初始化应用
export function initApp() {
    if (isInitialized) return;
    
    console.log('初始化甘特图应用...');
    
    try {
        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', performInit);
        } else {
            performInit();
        }
    } catch (error) {
        console.error('应用初始化失败:', error);
    }
}

// 执行初始化
function performInit() {
    try {
        // 1. 初始化DOM元素引用
        console.log('1. 初始化DOM元素引用...');
        initUIElements();
        initEventElements();
        
        // 2. 绑定事件监听器
        console.log('2. 绑定事件监听器...');
        bindEventListeners();
        
        // 3. 初始化拖拽功能
        console.log('3. 初始化拖拽功能...');
        initDragHandlers();
        
        // 4. 添加示例数据（如果没有数据）
        console.log('4. 检查并添加示例数据...');
        if (tasks.length === 0) {
            addSampleData();
        }
        
        // 5. 渲染界面
        console.log('5. 渲染界面...');
        renderTasks();

        // 延迟渲染甘特图，确保DOM元素已准备好
        setTimeout(() => {
            renderGanttChart();
        }, 100);
        
        // 6. 更新界面状态
        console.log('6. 更新界面状态...');
        updateToolbarButtons();
        updateDateRangeDisplay();
        adjustGanttBodyHeight();
        
        // 7. 设置滚动同步
        console.log('7. 设置滚动同步...');
        setupScrollSync();
        
        // 8. 设置窗口大小变化监听
        console.log('8. 设置窗口大小变化监听...');
        setupResizeHandler();
        
        // 9. 标记初始化完成
        isInitialized = true;
        console.log('✅ 甘特图应用初始化完成');
        
        // 10. 延迟执行一些优化操作
        setTimeout(() => {
            fixScrollAlignment();
            initDragHandlers(); // 重新初始化拖拽监听器
        }, 100);
        
    } catch (error) {
        console.error('初始化过程中发生错误:', error);
        alert('应用初始化失败，请刷新页面重试');
    }
}

// 设置滚动同步
function setupScrollSync() {
    const unifiedBody = document.querySelector('.unified-body');
    const taskTableBody = document.getElementById('taskTableBody');
    const ganttChart = document.getElementById('ganttChart');
    
    if (unifiedBody && taskTableBody && ganttChart) {
        unifiedBody.addEventListener('scroll', () => {
            taskTableBody.scrollTop = unifiedBody.scrollTop;
            ganttChart.scrollTop = unifiedBody.scrollTop;
        });
        
        console.log('✅ 滚动同步设置完成');
    }
}

// 设置窗口大小变化处理
function setupResizeHandler() {
    let resizeTimer;
    
    window.addEventListener('resize', () => {
        // 防抖处理
        clearTimeout(resizeTimer);
        resizeTimer = setTimeout(() => {
            console.log('窗口大小变化，重新渲染甘特图...');
            renderGanttChart();
            adjustGanttBodyHeight();
            fixScrollAlignment();
            
            // 重新初始化拖拽监听器
            setTimeout(() => {
                initDragHandlers();
            }, 100);
        }, 250);
    });
    
    console.log('✅ 窗口大小变化监听设置完成');
}

// 刷新应用
export function refreshApp() {
    console.log('刷新应用...');
    
    try {
        renderTasks();
        renderGanttChart();
        updateToolbarButtons();
        updateDateRangeDisplay();
        adjustGanttBodyHeight();
        
        // 重新初始化拖拽监听器
        setTimeout(() => {
            initDragHandlers();
            fixScrollAlignment();
        }, 100);
        
        console.log('✅ 应用刷新完成');
    } catch (error) {
        console.error('应用刷新失败:', error);
    }
}

// 重置应用
export function resetApp() {
    if (confirm('确定要重置应用吗？这将清除所有数据并恢复到初始状态。')) {
        try {
            // 清除本地存储
            localStorage.removeItem('ganttTasks');
            localStorage.removeItem('ganttGroups');
            localStorage.removeItem('ganttBorderSettings');
            localStorage.removeItem('ganttColumnSettings');
            localStorage.removeItem('ganttColumnWidths');
            
            // 重新加载页面
            window.location.reload();
        } catch (error) {
            console.error('应用重置失败:', error);
            alert('重置失败，请手动刷新页面');
        }
    }
}

// 获取应用状态
export function getAppStatus() {
    return {
        initialized: isInitialized,
        taskCount: tasks.length,
        version: '2.1.0',
        modules: [
            'data-manager',
            'ui-renderer', 
            'event-handler',
            'drag-handler',
            'import-export',
            'utils'
        ]
    };
}

// 调试函数
export function debugApp() {
    const status = getAppStatus();
    console.log('=== 甘特图应用调试信息 ===');
    console.log('应用状态:', status);
    console.log('任务数据:', tasks);
    console.log('DOM元素检查:');
    console.log('- taskTableBody:', document.getElementById('taskTableBody'));
    console.log('- ganttChart:', document.getElementById('ganttChart'));
    console.log('- timeRuler:', document.getElementById('timeRuler'));
    console.log('- customStep:', document.getElementById('customStep'));
    console.log('- selectAll:', document.getElementById('selectAll'));
    console.log('========================');
    
    return status;
}

// 导出全局函数供HTML调用
window.GanttApp = {
    init: initApp,
    refresh: refreshApp,
    reset: resetApp,
    debug: debugApp,
    getStatus: getAppStatus
};

// 自动初始化
initApp();
