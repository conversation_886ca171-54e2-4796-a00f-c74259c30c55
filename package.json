{"name": "gantt-chart-management-system", "version": "2.1.0", "description": "施工进度甘特图管理系统 - 模块化版本", "main": "server.js", "scripts": {"start": "node server.js", "dev": "node server.js", "serve": "node server.js", "python-server": "python -m http.server 8080", "build": "echo '项目已经是静态文件，无需构建'", "test": "echo '暂无测试脚本'", "clean": "node clean.js"}, "keywords": ["gantt", "chart", "project", "management", "construction", "schedule"], "author": "Developer", "license": "MIT", "repository": {"type": "git", "url": "."}, "engines": {"node": ">=14.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}